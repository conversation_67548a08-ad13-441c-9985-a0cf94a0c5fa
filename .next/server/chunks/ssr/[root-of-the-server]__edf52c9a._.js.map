{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Gokstad/thewizardweb/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\nconst Navigation = () => {\n  const pathname = usePathname();\n\n  const navItems = [\n    { href: '/', label: 'Home', icon: '🏠' },\n    { href: '/learn', label: 'Learn', icon: '📚' },\n    { href: '/challenges', label: 'Challenges', icon: '🎯' },\n  ];\n\n  return (\n    <nav className=\"bg-white/10 backdrop-blur-sm border-b border-white/20\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <span className=\"text-2xl\">🧙‍♂️</span>\n            <span className=\"text-white font-bold text-xl\">The Wizard Web</span>\n          </Link>\n\n          <div className=\"flex space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                  pathname === item.href\n                    ? 'bg-white/20 text-white'\n                    : 'text-gray-300 hover:text-white hover:bg-white/10'\n                }`}\n              >\n                <span>{item.icon}</span>\n                <span>{item.label}</span>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,WAAW,IAAA,iJAAW;IAE5B,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM;QAAK;QACvC;YAAE,MAAM;YAAU,OAAO;YAAS,MAAM;QAAK;QAC7C;YAAE,MAAM;YAAe,OAAO;YAAc,MAAM;QAAK;KACxD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uKAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;;kCAGjD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,uKAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,uFAAuF,EACjG,aAAa,KAAK,IAAI,GAClB,2BACA,oDACJ;;kDAEF,8OAAC;kDAAM,KAAK,IAAI;;;;;;kDAChB,8OAAC;kDAAM,KAAK,KAAK;;;;;;;+BATZ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAiB9B;uCAEe", "debugId": null}}]}