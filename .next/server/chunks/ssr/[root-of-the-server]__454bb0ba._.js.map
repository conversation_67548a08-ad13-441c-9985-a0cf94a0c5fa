{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Gokstad/thewizardweb/src/app/learn/page.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nconst topics = [\n  {\n    id: 'typescript',\n    title: 'TypeScript Fundamentals',\n    description: 'Learn the basics of TypeScript and how it enhances JavaScript development.',\n    icon: '📘',\n    difficulty: 'Beginner',\n    duration: '30 min',\n  },\n  {\n    id: 'css',\n    title: 'Modern CSS Techniques',\n    description: 'Master Flexbox, Grid, and modern CSS features for responsive design.',\n    icon: '🎨',\n    difficulty: 'Intermediate',\n    duration: '45 min',\n  },\n  {\n    id: 'react',\n    title: 'React Components',\n    description: 'Build reusable components and understand React fundamentals.',\n    icon: '⚛️',\n    difficulty: 'Intermediate',\n    duration: '60 min',\n  },\n  {\n    id: 'zustand',\n    title: 'State Management with Zustand',\n    description: 'Learn how to manage application state efficiently with Zustand.',\n    icon: '🐻',\n    difficulty: 'Advanced',\n    duration: '40 min',\n  },\n  {\n    id: 'nextjs',\n    title: 'Next.js Essentials',\n    description: 'Discover the power of Next.js for full-stack React applications.',\n    icon: '▲',\n    difficulty: 'Advanced',\n    duration: '90 min',\n  },\n  {\n    id: 'animations',\n    title: 'Web Animations',\n    description: 'Create engaging user experiences with CSS and JavaScript animations.',\n    icon: '✨',\n    difficulty: 'Intermediate',\n    duration: '50 min',\n  },\n];\n\nconst getDifficultyColor = (difficulty: string) => {\n  switch (difficulty) {\n    case 'Beginner':\n      return 'bg-green-500';\n    case 'Intermediate':\n      return 'bg-yellow-500';\n    case 'Advanced':\n      return 'bg-red-500';\n    default:\n      return 'bg-gray-500';\n  }\n};\n\nexport default function LearnPage() {\n  return (\n    <div className=\"min-h-screen py-12\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-white mb-4\">\n            📚 Learning Path\n          </h1>\n          <p className=\"text-xl text-gray-200 max-w-2xl mx-auto\">\n            Choose a topic to start your magical journey into web development. \n            Each lesson includes interactive examples and hands-on practice.\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {topics.map((topic) => (\n            <Link\n              key={topic.id}\n              href={`/learn/${topic.id}`}\n              className=\"group bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all duration-300 hover:scale-105 border border-white/10\"\n            >\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"text-4xl\">{topic.icon}</div>\n                <div className=\"flex flex-col items-end space-y-2\">\n                  <span className={`px-2 py-1 rounded-full text-xs font-semibold text-white ${getDifficultyColor(topic.difficulty)}`}>\n                    {topic.difficulty}\n                  </span>\n                  <span className=\"text-sm text-gray-300\">{topic.duration}</span>\n                </div>\n              </div>\n              \n              <h3 className=\"text-xl font-semibold text-white mb-2 group-hover:text-purple-200 transition-colors\">\n                {topic.title}\n              </h3>\n              \n              <p className=\"text-gray-300 text-sm leading-relaxed\">\n                {topic.description}\n              </p>\n              \n              <div className=\"mt-4 flex items-center text-purple-300 text-sm font-medium\">\n                Start Learning\n                <span className=\"ml-2 group-hover:translate-x-1 transition-transform\">→</span>\n              </div>\n            </Link>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEA,MAAM,SAAS;IACb;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;CACD;AAED,MAAM,qBAAqB,CAAC;IAC1B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,uKAAI;4BAEH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;4BAC1B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAY,MAAM,IAAI;;;;;;sDACrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAW,CAAC,wDAAwD,EAAE,mBAAmB,MAAM,UAAU,GAAG;8DAC/G,MAAM,UAAU;;;;;;8DAEnB,8OAAC;oDAAK,WAAU;8DAAyB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;8CAI3D,8OAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAGd,8OAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;8CAGpB,8OAAC;oCAAI,WAAU;;wCAA6D;sDAE1E,8OAAC;4CAAK,WAAU;sDAAsD;;;;;;;;;;;;;2BAxBnE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;AAgC3B", "debugId": null}}]}