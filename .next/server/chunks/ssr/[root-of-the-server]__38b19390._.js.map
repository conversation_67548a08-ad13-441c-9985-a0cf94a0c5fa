{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Gokstad/thewizardweb/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\">\n      {/* Hero Section */}\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"text-center\">\n          <h1 className=\"text-6xl font-bold text-white mb-6\">\n            🧙‍♂️ The Wizard Web\n          </h1>\n          <p className=\"text-xl text-gray-200 mb-8 max-w-2xl mx-auto\">\n            Master the magical arts of web development! Learn modern web technologies\n            through interactive lessons and challenging quests.\n          </p>\n\n          <div className=\"flex gap-6 justify-center flex-wrap\">\n            <Link\n              href=\"/learn\"\n              className=\"bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors shadow-lg\"\n            >\n              Start Learning ✨\n            </Link>\n            <Link\n              href=\"/challenges\"\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors shadow-lg\"\n            >\n              Take Challenges 🎯\n            </Link>\n          </div>\n        </div>\n\n        {/* Features Section */}\n        <div className=\"mt-20 grid md:grid-cols-3 gap-8\">\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-4xl mb-4\">📚</div>\n            <h3 className=\"text-xl font-semibold text-white mb-2\">Interactive Learning</h3>\n            <p className=\"text-gray-300\">\n              Learn TypeScript, CSS, React, and more through hands-on examples and live code demonstrations.\n            </p>\n          </div>\n\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-4xl mb-4\">🎮</div>\n            <h3 className=\"text-xl font-semibold text-white mb-2\">Gamified Challenges</h3>\n            <p className=\"text-gray-300\">\n              Test your skills with interactive quizzes and coding challenges. Track your progress and earn achievements.\n            </p>\n          </div>\n\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center\">\n            <div className=\"text-4xl mb-4\">🚀</div>\n            <h3 className=\"text-xl font-semibold text-white mb-2\">Modern Stack</h3>\n            <p className=\"text-gray-300\">\n              Built with Next.js, TypeScript, and Tailwind CSS. Learn the tools that power today's web.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAK5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC", "debugId": null}}]}