{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Gokstad/thewizardweb/src/app/learn/typescript/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nconst CodeExample = ({ title, code, explanation }: { title: string; code: string; explanation: string }) => {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  return (\n    <div className=\"bg-white/5 rounded-lg p-6 border border-white/10\">\n      <h3 className=\"text-lg font-semibold text-white mb-3\">{title}</h3>\n      <div className=\"bg-gray-900 rounded-md p-4 mb-4\">\n        <pre className=\"text-green-400 text-sm overflow-x-auto\">\n          <code>{code}</code>\n        </pre>\n      </div>\n      <button\n        onClick={() => setIsExpanded(!isExpanded)}\n        className=\"text-purple-300 hover:text-purple-200 text-sm font-medium mb-2\"\n      >\n        {isExpanded ? '▼' : '▶'} Explanation\n      </button>\n      {isExpanded && (\n        <p className=\"text-gray-300 text-sm leading-relaxed\">{explanation}</p>\n      )}\n    </div>\n  );\n};\n\nexport default function TypeScriptLesson() {\n  return (\n    <div className=\"min-h-screen py-12\">\n      <div className=\"container mx-auto px-4 max-w-4xl\">\n        <div className=\"mb-8\">\n          <Link \n            href=\"/learn\" \n            className=\"text-purple-300 hover:text-purple-200 text-sm font-medium mb-4 inline-block\"\n          >\n            ← Back to Learning Path\n          </Link>\n          <h1 className=\"text-4xl font-bold text-white mb-4\">\n            📘 TypeScript Fundamentals\n          </h1>\n          <p className=\"text-xl text-gray-200\">\n            Learn how TypeScript adds type safety to JavaScript and makes your code more robust.\n          </p>\n        </div>\n\n        <div className=\"space-y-8\">\n          <section className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/10\">\n            <h2 className=\"text-2xl font-semibold text-white mb-4\">What is TypeScript?</h2>\n            <p className=\"text-gray-200 leading-relaxed mb-4\">\n              TypeScript is a programming language developed by Microsoft that builds on JavaScript \n              by adding static type definitions. It helps catch errors early in development and \n              makes your code more maintainable.\n            </p>\n            <div className=\"bg-blue-900/30 border-l-4 border-blue-400 p-4 rounded\">\n              <p className=\"text-blue-200 text-sm\">\n                💡 <strong>Fun Fact:</strong> TypeScript code compiles to clean, readable JavaScript \n                that runs anywhere JavaScript runs.\n              </p>\n            </div>\n          </section>\n\n          <section>\n            <h2 className=\"text-2xl font-semibold text-white mb-6\">Key Concepts</h2>\n            <div className=\"space-y-6\">\n              <CodeExample\n                title=\"Basic Types\"\n                code={`// Basic type annotations\nlet name: string = \"Wizard\";\nlet age: number = 25;\nlet isLearning: boolean = true;\n\n// Arrays\nlet skills: string[] = [\"HTML\", \"CSS\", \"JavaScript\"];\nlet scores: number[] = [95, 87, 92];`}\n                explanation=\"TypeScript allows you to specify the type of variables, which helps catch errors at compile time. The basic types include string, number, boolean, and arrays.\"\n              />\n\n              <CodeExample\n                title=\"Interfaces\"\n                code={`// Define the shape of an object\ninterface Student {\n  name: string;\n  age: number;\n  courses: string[];\n  isActive?: boolean; // Optional property\n}\n\nconst student: Student = {\n  name: \"Alex\",\n  age: 22,\n  courses: [\"TypeScript\", \"React\"]\n};`}\n                explanation=\"Interfaces define the structure of objects. They're like contracts that ensure objects have the required properties with correct types. The '?' makes a property optional.\"\n              />\n\n              <CodeExample\n                title=\"Functions with Types\"\n                code={`// Function with typed parameters and return type\nfunction calculateGrade(score: number): string {\n  if (score >= 90) return \"A\";\n  if (score >= 80) return \"B\";\n  if (score >= 70) return \"C\";\n  return \"F\";\n}\n\n// Arrow function with types\nconst multiply = (a: number, b: number): number => a * b;`}\n                explanation=\"Functions can have typed parameters and return types. This ensures you pass the right types of arguments and return the expected type.\"\n              />\n\n              <CodeExample\n                title=\"Union Types\"\n                code={`// A variable that can be multiple types\nlet id: string | number;\nid = \"ABC123\";  // Valid\nid = 12345;     // Also valid\n\n// Function that accepts multiple types\nfunction formatId(id: string | number): string {\n  return \\`ID: \\${id}\\`;\n}`}\n                explanation=\"Union types allow a variable to be one of several types. This is useful when a value could legitimately be different types in different situations.\"\n              />\n            </div>\n          </section>\n\n          <section className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/10\">\n            <h2 className=\"text-2xl font-semibold text-white mb-4\">Why Use TypeScript?</h2>\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-purple-300 mb-2\">✅ Benefits</h3>\n                <ul className=\"text-gray-200 space-y-2 text-sm\">\n                  <li>• Catch errors at compile time</li>\n                  <li>• Better IDE support and autocomplete</li>\n                  <li>• Improved code documentation</li>\n                  <li>• Easier refactoring</li>\n                  <li>• Better team collaboration</li>\n                </ul>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-yellow-300 mb-2\">⚠️ Considerations</h3>\n                <ul className=\"text-gray-200 space-y-2 text-sm\">\n                  <li>• Learning curve for beginners</li>\n                  <li>• Additional build step required</li>\n                  <li>• More verbose than plain JavaScript</li>\n                  <li>• Setup complexity for new projects</li>\n                </ul>\n              </div>\n            </div>\n          </section>\n\n          <div className=\"text-center\">\n            <Link\n              href=\"/challenges\"\n              className=\"bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors shadow-lg inline-block\"\n            >\n              Ready for Challenges? 🎯\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,cAAc;QAAC,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAwD;;IACrG,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAyC;;;;;;0BACvD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAM;;;;;;;;;;;;;;;;0BAGX,6LAAC;gBACC,SAAS,IAAM,cAAc,CAAC;gBAC9B,WAAU;;oBAET,aAAa,MAAM;oBAAI;;;;;;;YAEzB,4BACC,6LAAC;gBAAE,WAAU;0BAAyC;;;;;;;;;;;;AAI9D;GAtBM;KAAA;AAwBS,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0KAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAKlD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;4CAAwB;0DAChC,6LAAC;0DAAO;;;;;;4CAAkB;;;;;;;;;;;;;;;;;;sCAMnC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,OAAM;4CACN,MAAO;4CAQP,aAAY;;;;;;sDAGd,6LAAC;4CACC,OAAM;4CACN,MAAO;4CAaP,aAAY;;;;;;sDAGd,6LAAC;4CACC,OAAM;4CACN,MAAO;4CAUP,aAAY;;;;;;sDAGd,6LAAC;4CACC,OAAM;4CACN,MAAO;4CASP,aAAY;;;;;;;;;;;;;;;;;;sCAKlB,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;sDAGR,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;MAzIwB", "debugId": null}}]}