{"version": 3, "sources": ["../../../src/server/lib/utils.ts"], "sourcesContent": ["import { parseArgs } from 'node:util'\nimport { InvalidArgumentError } from 'next/dist/compiled/commander'\n\nexport function printAndExit(message: string, code = 1) {\n  if (code === 0) {\n    console.log(message)\n  } else {\n    console.error(message)\n  }\n\n  return process.exit(code)\n}\n\nconst parseNodeArgs = (args: string[]) => {\n  const { values, tokens } = parseArgs({ args, strict: false, tokens: true })\n\n  // For the `NODE_OPTIONS`, we support arguments with values without the `=`\n  // sign. We need to parse them manually.\n  let orphan = null\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i]\n\n    if (token.kind === 'option-terminator') {\n      break\n    }\n\n    // When we encounter an option, if it's value is undefined, we should check\n    // to see if the following tokens are positional parameters. If they are,\n    // then the option is orphaned, and we can assign it.\n    if (token.kind === 'option') {\n      orphan = typeof token.value === 'undefined' ? token : null\n      continue\n    }\n\n    // If the token isn't a positional one, then we can't assign it to the found\n    // orphaned option.\n    if (token.kind !== 'positional') {\n      orphan = null\n      continue\n    }\n\n    // If we don't have an orphan, then we can skip this token.\n    if (!orphan) {\n      continue\n    }\n\n    // If the token is a positional one, and it has a value, so add it to the\n    // values object. If it already exists, append it with a space.\n    if (orphan.name in values && typeof values[orphan.name] === 'string') {\n      values[orphan.name] += ` ${token.value}`\n    } else {\n      values[orphan.name] = token.value\n    }\n  }\n\n  return values\n}\n\n/**\n * Tokenizes the arguments string into an array of strings, supporting quoted\n * values and escaped characters.\n * Converted from: https://github.com/nodejs/node/blob/c29d53c5cfc63c5a876084e788d70c9e87bed880/src/node_options.cc#L1401\n *\n * @param input The arguments string to be tokenized.\n * @returns An array of strings with the tokenized arguments.\n */\nexport const tokenizeArgs = (input: string): string[] => {\n  let args: string[] = []\n  let isInString = false\n  let willStartNewArg = true\n\n  for (let i = 0; i < input.length; i++) {\n    let char = input[i]\n\n    // Skip any escaped characters in strings.\n    if (char === '\\\\' && isInString) {\n      // Ensure we don't have an escape character at the end.\n      if (input.length === i + 1) {\n        throw new Error('Invalid escape character at the end.')\n      }\n\n      // Skip the next character.\n      char = input[++i]\n    }\n    // If we find a space outside of a string, we should start a new argument.\n    else if (char === ' ' && !isInString) {\n      willStartNewArg = true\n      continue\n    }\n\n    // If we find a quote, we should toggle the string flag.\n    else if (char === '\"') {\n      isInString = !isInString\n      continue\n    }\n\n    // If we're starting a new argument, we should add it to the array.\n    if (willStartNewArg) {\n      args.push(char)\n      willStartNewArg = false\n    }\n    // Otherwise, add it to the last argument.\n    else {\n      args[args.length - 1] += char\n    }\n  }\n\n  if (isInString) {\n    throw new Error('Unterminated string')\n  }\n\n  return args\n}\n\n/**\n * Get the node options from the environment variable `NODE_OPTIONS` and returns\n * them as an array of strings.\n *\n * @returns An array of strings with the node options.\n */\nconst getNodeOptionsArgs = () => {\n  if (!process.env.NODE_OPTIONS) return []\n\n  return tokenizeArgs(process.env.NODE_OPTIONS)\n}\n\n/**\n * The debug address is in the form of `[host:]port`. The host is optional.\n */\ntype DebugAddress = {\n  host: string | undefined\n  port: number\n}\n\n/**\n * Formats the debug address into a string.\n */\nexport const formatDebugAddress = ({ host, port }: DebugAddress): string => {\n  if (host) return `${host}:${port}`\n  return `${port}`\n}\n\n/**\n * Get's the debug address from the `NODE_OPTIONS` environment variable. If the\n * address is not found, it returns the default host (`undefined`) and port\n * (`9229`).\n *\n * @returns An object with the host and port of the debug address.\n */\nexport const getParsedDebugAddress = (): DebugAddress => {\n  const args = getNodeOptionsArgs()\n  if (args.length === 0) return { host: undefined, port: 9229 }\n\n  const parsed = parseNodeArgs(args)\n\n  // We expect to find the debug port in one of these options. The first one\n  // found will be used.\n  const address =\n    parsed.inspect ?? parsed['inspect-brk'] ?? parsed['inspect_brk']\n\n  if (!address || typeof address !== 'string') {\n    return { host: undefined, port: 9229 }\n  }\n\n  // The address is in the form of `[host:]port`. Let's parse the address.\n  if (address.includes(':')) {\n    const [host, port] = address.split(':')\n    return { host, port: parseInt(port, 10) }\n  }\n\n  return { host: undefined, port: parseInt(address, 10) }\n}\n\n/**\n * Get the debug address from the `NODE_OPTIONS` environment variable and format\n * it into a string.\n *\n * @returns A string with the formatted debug address.\n */\nexport const getFormattedDebugAddress = () =>\n  formatDebugAddress(getParsedDebugAddress())\n\n/**\n * Stringify the arguments to be used in a command line. It will ignore any\n * argument that has a value of `undefined`.\n *\n * @param args The arguments to be stringified.\n * @returns A string with the arguments.\n */\nexport function formatNodeOptions(\n  args: Record<string, string | boolean | undefined>\n): string {\n  return Object.entries(args)\n    .map(([key, value]) => {\n      if (value === true) {\n        return `--${key}`\n      }\n\n      if (value) {\n        return `--${key}=${\n          // Values with spaces need to be quoted. We use JSON.stringify to\n          // also escape any nested quotes.\n          value.includes(' ') && !value.startsWith('\"')\n            ? JSON.stringify(value)\n            : value\n        }`\n      }\n\n      return null\n    })\n    .filter((arg) => arg !== null)\n    .join(' ')\n}\n\n/**\n * Get the node options from the `NODE_OPTIONS` environment variable and parse\n * them into an object without the inspect options.\n *\n * @returns An object with the parsed node options.\n */\nexport function getParsedNodeOptionsWithoutInspect() {\n  const args = getNodeOptionsArgs()\n  if (args.length === 0) return {}\n\n  const parsed = parseNodeArgs(args)\n\n  // Remove inspect options.\n  delete parsed.inspect\n  delete parsed['inspect-brk']\n  delete parsed['inspect_brk']\n\n  return parsed\n}\n\n/**\n * Get the node options from the `NODE_OPTIONS` environment variable and format\n * them into a string without the inspect options.\n *\n * @returns A string with the formatted node options.\n */\nexport function getFormattedNodeOptionsWithoutInspect() {\n  const args = getParsedNodeOptionsWithoutInspect()\n  if (Object.keys(args).length === 0) return ''\n\n  return formatNodeOptions(args)\n}\n\n/**\n * Check if the value is a valid positive integer and parse it. If it's not, it will throw an error.\n *\n * @param value The value to be parsed.\n */\nexport function parseValidPositiveInteger(value: string): number {\n  const parsedValue = parseInt(value, 10)\n\n  if (isNaN(parsedValue) || !isFinite(parsedValue) || parsedValue < 0) {\n    throw new InvalidArgumentError(`'${value}' is not a non-negative number.`)\n  }\n  return parsedValue\n}\n\nexport const RESTART_EXIT_CODE = 77\n\nexport type NodeInspectType = 'inspect' | 'inspect-brk' | undefined\n\n/**\n * Get the debug type from the `NODE_OPTIONS` environment variable.\n */\nexport function getNodeDebugType(): NodeInspectType {\n  const args = [...process.execArgv, ...getNodeOptionsArgs()]\n  if (args.length === 0) return\n\n  const parsed = parseNodeArgs(args)\n\n  if (parsed.inspect) return 'inspect'\n  if (parsed['inspect-brk'] || parsed['inspect_brk']) return 'inspect-brk'\n}\n\n/**\n * Get the `max-old-space-size` value from the `NODE_OPTIONS` environment\n * variable.\n *\n * @returns The value of the `max-old-space-size` option as a number.\n */\nexport function getMaxOldSpaceSize() {\n  const args = getNodeOptionsArgs()\n  if (args.length === 0) return\n\n  const parsed = parseNodeArgs(args)\n\n  const size = parsed['max-old-space-size'] || parsed['max_old_space_size']\n  if (!size || typeof size !== 'string') return\n\n  return parseInt(size, 10)\n}\n"], "names": ["RESTART_EXIT_CODE", "formatDebugAddress", "formatNodeOptions", "getFormattedDebugAddress", "getFormattedNodeOptionsWithoutInspect", "getMaxOldSpaceSize", "getNodeDebugType", "getParsedDebugAddress", "getParsedNodeOptionsWithoutInspect", "parseValidPositiveInteger", "printAndExit", "tokenizeArgs", "message", "code", "console", "log", "error", "process", "exit", "parseNodeArgs", "args", "values", "tokens", "parseArgs", "strict", "orphan", "i", "length", "token", "kind", "value", "name", "input", "isInString", "willStartNewArg", "char", "Error", "push", "getNodeOptionsArgs", "env", "NODE_OPTIONS", "host", "port", "undefined", "parsed", "address", "inspect", "includes", "split", "parseInt", "Object", "entries", "map", "key", "startsWith", "JSON", "stringify", "filter", "arg", "join", "keys", "parsedValue", "isNaN", "isFinite", "InvalidArgumentError", "execArgv", "size"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAqQaA,iBAAiB;eAAjBA;;IA5HAC,kBAAkB;eAAlBA;;IAoDGC,iBAAiB;eAAjBA;;IAVHC,wBAAwB;eAAxBA;;IA6DGC,qCAAqC;eAArCA;;IA4CAC,kBAAkB;eAAlBA;;IAhBAC,gBAAgB;eAAhBA;;IAvHHC,qBAAqB;eAArBA;;IAuEGC,kCAAkC;eAAlCA;;IAgCAC,yBAAyB;eAAzBA;;IAzPAC,YAAY;eAAZA;;IA+DHC,YAAY;eAAZA;;;0BAlEa;2BACW;AAE9B,SAASD,aAAaE,OAAe,EAAEC,OAAO,CAAC;IACpD,IAAIA,SAAS,GAAG;QACdC,QAAQC,GAAG,CAACH;IACd,OAAO;QACLE,QAAQE,KAAK,CAACJ;IAChB;IAEA,OAAOK,QAAQC,IAAI,CAACL;AACtB;AAEA,MAAMM,gBAAgB,CAACC;IACrB,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,GAAGC,IAAAA,mBAAS,EAAC;QAAEH;QAAMI,QAAQ;QAAOF,QAAQ;IAAK;IAEzE,2EAA2E;IAC3E,wCAAwC;IACxC,IAAIG,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,OAAOK,MAAM,EAAED,IAAK;QACtC,MAAME,QAAQN,MAAM,CAACI,EAAE;QAEvB,IAAIE,MAAMC,IAAI,KAAK,qBAAqB;YACtC;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,qDAAqD;QACrD,IAAID,MAAMC,IAAI,KAAK,UAAU;YAC3BJ,SAAS,OAAOG,MAAME,KAAK,KAAK,cAAcF,QAAQ;YACtD;QACF;QAEA,4EAA4E;QAC5E,mBAAmB;QACnB,IAAIA,MAAMC,IAAI,KAAK,cAAc;YAC/BJ,SAAS;YACT;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAACA,QAAQ;YACX;QACF;QAEA,yEAAyE;QACzE,+DAA+D;QAC/D,IAAIA,OAAOM,IAAI,IAAIV,UAAU,OAAOA,MAAM,CAACI,OAAOM,IAAI,CAAC,KAAK,UAAU;YACpEV,MAAM,CAACI,OAAOM,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEH,MAAME,KAAK,EAAE;QAC1C,OAAO;YACLT,MAAM,CAACI,OAAOM,IAAI,CAAC,GAAGH,MAAME,KAAK;QACnC;IACF;IAEA,OAAOT;AACT;AAUO,MAAMV,eAAe,CAACqB;IAC3B,IAAIZ,OAAiB,EAAE;IACvB,IAAIa,aAAa;IACjB,IAAIC,kBAAkB;IAEtB,IAAK,IAAIR,IAAI,GAAGA,IAAIM,MAAML,MAAM,EAAED,IAAK;QACrC,IAAIS,OAAOH,KAAK,CAACN,EAAE;QAEnB,0CAA0C;QAC1C,IAAIS,SAAS,QAAQF,YAAY;YAC/B,uDAAuD;YACvD,IAAID,MAAML,MAAM,KAAKD,IAAI,GAAG;gBAC1B,MAAM,qBAAiD,CAAjD,IAAIU,MAAM,yCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAgD;YACxD;YAEA,2BAA2B;YAC3BD,OAAOH,KAAK,CAAC,EAAEN,EAAE;QACnB,OAEK,IAAIS,SAAS,OAAO,CAACF,YAAY;YACpCC,kBAAkB;YAClB;QACF,OAGK,IAAIC,SAAS,KAAK;YACrBF,aAAa,CAACA;YACd;QACF;QAEA,mEAAmE;QACnE,IAAIC,iBAAiB;YACnBd,KAAKiB,IAAI,CAACF;YACVD,kBAAkB;QACpB,OAEK;YACHd,IAAI,CAACA,KAAKO,MAAM,GAAG,EAAE,IAAIQ;QAC3B;IACF;IAEA,IAAIF,YAAY;QACd,MAAM,qBAAgC,CAAhC,IAAIG,MAAM,wBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA+B;IACvC;IAEA,OAAOhB;AACT;AAEA;;;;;CAKC,GACD,MAAMkB,qBAAqB;IACzB,IAAI,CAACrB,QAAQsB,GAAG,CAACC,YAAY,EAAE,OAAO,EAAE;IAExC,OAAO7B,aAAaM,QAAQsB,GAAG,CAACC,YAAY;AAC9C;AAaO,MAAMvC,qBAAqB,CAAC,EAAEwC,IAAI,EAAEC,IAAI,EAAgB;IAC7D,IAAID,MAAM,OAAO,GAAGA,KAAK,CAAC,EAAEC,MAAM;IAClC,OAAO,GAAGA,MAAM;AAClB;AASO,MAAMnC,wBAAwB;IACnC,MAAMa,OAAOkB;IACb,IAAIlB,KAAKO,MAAM,KAAK,GAAG,OAAO;QAAEc,MAAME;QAAWD,MAAM;IAAK;IAE5D,MAAME,SAASzB,cAAcC;IAE7B,0EAA0E;IAC1E,sBAAsB;IACtB,MAAMyB,UACJD,OAAOE,OAAO,IAAIF,MAAM,CAAC,cAAc,IAAIA,MAAM,CAAC,cAAc;IAElE,IAAI,CAACC,WAAW,OAAOA,YAAY,UAAU;QAC3C,OAAO;YAAEJ,MAAME;YAAWD,MAAM;QAAK;IACvC;IAEA,wEAAwE;IACxE,IAAIG,QAAQE,QAAQ,CAAC,MAAM;QACzB,MAAM,CAACN,MAAMC,KAAK,GAAGG,QAAQG,KAAK,CAAC;QACnC,OAAO;YAAEP;YAAMC,MAAMO,SAASP,MAAM;QAAI;IAC1C;IAEA,OAAO;QAAED,MAAME;QAAWD,MAAMO,SAASJ,SAAS;IAAI;AACxD;AAQO,MAAM1C,2BAA2B,IACtCF,mBAAmBM;AASd,SAASL,kBACdkB,IAAkD;IAElD,OAAO8B,OAAOC,OAAO,CAAC/B,MACnBgC,GAAG,CAAC,CAAC,CAACC,KAAKvB,MAAM;QAChB,IAAIA,UAAU,MAAM;YAClB,OAAO,CAAC,EAAE,EAAEuB,KAAK;QACnB;QAEA,IAAIvB,OAAO;YACT,OAAO,CAAC,EAAE,EAAEuB,IAAI,CAAC,EACf,iEAAiE;YACjE,iCAAiC;YACjCvB,MAAMiB,QAAQ,CAAC,QAAQ,CAACjB,MAAMwB,UAAU,CAAC,OACrCC,KAAKC,SAAS,CAAC1B,SACfA,OACJ;QACJ;QAEA,OAAO;IACT,GACC2B,MAAM,CAAC,CAACC,MAAQA,QAAQ,MACxBC,IAAI,CAAC;AACV;AAQO,SAASnD;IACd,MAAMY,OAAOkB;IACb,IAAIlB,KAAKO,MAAM,KAAK,GAAG,OAAO,CAAC;IAE/B,MAAMiB,SAASzB,cAAcC;IAE7B,0BAA0B;IAC1B,OAAOwB,OAAOE,OAAO;IACrB,OAAOF,MAAM,CAAC,cAAc;IAC5B,OAAOA,MAAM,CAAC,cAAc;IAE5B,OAAOA;AACT;AAQO,SAASxC;IACd,MAAMgB,OAAOZ;IACb,IAAI0C,OAAOU,IAAI,CAACxC,MAAMO,MAAM,KAAK,GAAG,OAAO;IAE3C,OAAOzB,kBAAkBkB;AAC3B;AAOO,SAASX,0BAA0BqB,KAAa;IACrD,MAAM+B,cAAcZ,SAASnB,OAAO;IAEpC,IAAIgC,MAAMD,gBAAgB,CAACE,SAASF,gBAAgBA,cAAc,GAAG;QACnE,MAAM,IAAIG,+BAAoB,CAAC,CAAC,CAAC,EAAElC,MAAM,+BAA+B,CAAC;IAC3E;IACA,OAAO+B;AACT;AAEO,MAAM7D,oBAAoB;AAO1B,SAASM;IACd,MAAMc,OAAO;WAAIH,QAAQgD,QAAQ;WAAK3B;KAAqB;IAC3D,IAAIlB,KAAKO,MAAM,KAAK,GAAG;IAEvB,MAAMiB,SAASzB,cAAcC;IAE7B,IAAIwB,OAAOE,OAAO,EAAE,OAAO;IAC3B,IAAIF,MAAM,CAAC,cAAc,IAAIA,MAAM,CAAC,cAAc,EAAE,OAAO;AAC7D;AAQO,SAASvC;IACd,MAAMe,OAAOkB;IACb,IAAIlB,KAAKO,MAAM,KAAK,GAAG;IAEvB,MAAMiB,SAASzB,cAAcC;IAE7B,MAAM8C,OAAOtB,MAAM,CAAC,qBAAqB,IAAIA,MAAM,CAAC,qBAAqB;IACzE,IAAI,CAACsB,QAAQ,OAAOA,SAAS,UAAU;IAEvC,OAAOjB,SAASiB,MAAM;AACxB", "ignoreList": [0]}