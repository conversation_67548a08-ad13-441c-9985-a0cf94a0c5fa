{"version": 3, "sources": ["../../../src/server/lib/source-maps.ts"], "sourcesContent": ["import type { SourceMap } from 'module'\nimport { LRUCache } from './lru-cache'\n\nfunction noSourceMap(): SourceMap | undefined {\n  return undefined\n}\n\n// Edge runtime does not implement `module`\nconst nativeFindSourceMap =\n  process.env.NEXT_RUNTIME === 'edge'\n    ? noSourceMap\n    : (require('module') as typeof import('module')).findSourceMap\n\n/**\n * https://tc39.es/source-map/#index-map\n */\ninterface IndexSourceMapSection {\n  offset: {\n    line: number\n    column: number\n  }\n  map: BasicSourceMapPayload\n}\n\n// TODO(veil): Upstream types\n/** https://tc39.es/ecma426/#sec-index-source-map */\ninterface IndexSourceMap {\n  version: number\n  file: string\n  sections: IndexSourceMapSection[]\n}\n\n/** https://tc39.es/ecma426/#sec-source-map-format */\nexport interface BasicSourceMapPayload {\n  version: number\n  // TODO: Move to https://github.com/jridgewell/sourcemaps which is actively maintained\n  /** WARNING: `file` is optional. */\n  file: string\n  sourceRoot?: string\n  // TODO: Move to https://github.com/jridgewell/sourcemaps which is actively maintained\n  /** WARNING: `sources[number]` can be `null`. */\n  sources: Array<string>\n  names: Array<string>\n  mappings: string\n  ignoreList?: number[]\n}\n\nexport type ModernSourceMapPayload = BasicSourceMapPayload | IndexSourceMap\n\nexport function sourceMapIgnoreListsEverything(\n  sourceMap: BasicSourceMapPayload\n): boolean {\n  return (\n    sourceMap.ignoreList !== undefined &&\n    sourceMap.sources.length === sourceMap.ignoreList.length\n  )\n}\n\n/**\n * Finds the sourcemap payload applicable to a given frame.\n * Equal to the input unless an Index Source Map is used.\n * @param line0 - The line number of the frame, 0-based.\n * @param column0 - The column number of the frame, 0-based.\n */\nexport function findApplicableSourceMapPayload(\n  line0: number,\n  column0: number,\n  payload: ModernSourceMapPayload\n): BasicSourceMapPayload | undefined {\n  if ('sections' in payload) {\n    if (payload.sections.length === 0) {\n      return undefined\n    }\n\n    // Sections must not overlap and must be sorted: https://tc39.es/source-map/#section-object\n    // Therefore the last section that has an offset less than or equal to the frame is the applicable one.\n    const sections = payload.sections\n    let left = 0\n    let right = sections.length - 1\n    let result: IndexSourceMapSection | null = null\n\n    while (left <= right) {\n      // fast Math.floor\n      const middle = ~~((left + right) / 2)\n      const section = sections[middle]\n      const offset = section.offset\n\n      if (\n        offset.line < line0 ||\n        (offset.line === line0 && offset.column <= column0)\n      ) {\n        result = section\n        left = middle + 1\n      } else {\n        right = middle - 1\n      }\n    }\n\n    return result === null ? undefined : result.map\n  } else {\n    return payload\n  }\n}\n\nconst didWarnAboutInvalidSourceMapDEV = new Set<string>()\n\nconst findSourceMap: (scriptNameOrSourceURL: string) => SourceMap | undefined =\n  process.env.NEXT_RUNTIME === 'nodejs' &&\n  process.versions.node?.startsWith('18')\n    ? // Node.js 18 has a horribly slow `findSourceMap` implementation\n      noSourceMap\n    : nativeFindSourceMap\n\nexport function filterStackFrameDEV(\n  sourceURL: string,\n  functionName: string,\n  line1: number,\n  column1: number\n): boolean {\n  if (sourceURL === '') {\n    // The default implementation filters out <anonymous> stack frames\n    // but we want to retain them because current Server Components and\n    // built-in Components in parent stacks don't have source location.\n    // Filter out frames that show up in Promises to get good names in React's\n    // Server Request track until we come up with a better heuristic.\n    return functionName !== 'new Promise'\n  }\n  if (sourceURL.startsWith('node:') || sourceURL.includes('node_modules')) {\n    return false\n  }\n  try {\n    // Node.js loads source maps eagerly so this call is cheap.\n    // TODO: ESM sourcemaps are O(1) but CommonJS sourcemaps are O(Number of CJS modules).\n    // Make sure this doesn't adversely affect performance when CJS is used by Next.js.\n    const sourceMap = findSourceMap(sourceURL)\n    if (sourceMap === undefined) {\n      // No source map assoicated.\n      // TODO: Node.js types should reflect that `findSourceMap` can return `undefined`.\n      return true\n    }\n    const sourceMapPayload = findApplicableSourceMapPayload(\n      line1 - 1,\n      column1 - 1,\n      sourceMap.payload\n    )\n    if (sourceMapPayload === undefined) {\n      // No source map section applicable to the frame.\n      return true\n    }\n    return !sourceMapIgnoreListsEverything(sourceMapPayload)\n  } catch (cause) {\n    if (process.env.NODE_ENV !== 'production') {\n      // TODO: Share cache with patch-error-inspect\n      if (!didWarnAboutInvalidSourceMapDEV.has(sourceURL)) {\n        didWarnAboutInvalidSourceMapDEV.add(sourceURL)\n        // We should not log an actual error instance here because that will re-enter\n        // this codepath during error inspection and could lead to infinite recursion.\n        console.error(\n          `${sourceURL}: Invalid source map. Only conformant source maps can be used to filter stack frames. Cause: ${cause}`\n        )\n      }\n    }\n\n    return true\n  }\n}\n\nconst invalidSourceMap = Symbol('invalid-source-map')\nconst sourceMapURLs = new LRUCache<string | typeof invalidSourceMap>(\n  512 * 1024 * 1024,\n  (url) =>\n    url === invalidSourceMap\n      ? // Ideally we'd account for key length. So we just guestimate a small source map\n        // so that we don't create a huge cache with empty source maps.\n        8 * 1024\n      : // these URLs contain only ASCII characters so .length is equal to Buffer.byteLength\n        url.length\n)\nexport function findSourceMapURLDEV(\n  scriptNameOrSourceURL: string\n): string | null {\n  let sourceMapURL = sourceMapURLs.get(scriptNameOrSourceURL)\n  if (sourceMapURL === undefined) {\n    let sourceMapPayload: ModernSourceMapPayload | undefined\n    try {\n      sourceMapPayload = findSourceMap(scriptNameOrSourceURL)?.payload\n    } catch (cause) {\n      console.error(\n        `${scriptNameOrSourceURL}: Invalid source map. Only conformant source maps can be used to find the original code. Cause: ${cause}`\n      )\n    }\n\n    if (sourceMapPayload === undefined) {\n      sourceMapURL = invalidSourceMap\n    } else {\n      // TODO: Might be more efficient to extract the relevant section from Index Maps.\n      // Unclear if that search is worth the smaller payload we have to stringify.\n      const sourceMapJSON = JSON.stringify(sourceMapPayload)\n      const sourceMapURLData = Buffer.from(sourceMapJSON, 'utf8').toString(\n        'base64'\n      )\n      sourceMapURL = `data:application/json;base64,${sourceMapURLData}`\n    }\n\n    sourceMapURLs.set(scriptNameOrSourceURL, sourceMapURL)\n  }\n\n  return sourceMapURL === invalidSourceMap ? null : sourceMapURL\n}\n\nexport function devirtualizeReactServerURL(sourceURL: string): string {\n  if (sourceURL.startsWith('about://React/')) {\n    // about://React/Server/file://<filename>?42 => file://<filename>\n    const envIdx = sourceURL.indexOf('/', 'about://React/'.length)\n    const suffixIdx = sourceURL.lastIndexOf('?')\n    if (envIdx > -1 && suffixIdx > -1) {\n      return decodeURI(sourceURL.slice(envIdx + 1, suffixIdx))\n    }\n  }\n  return sourceURL\n}\n\nfunction isAnonymousFrameLikelyJSNative(methodName: string): boolean {\n  // Anonymous frames can also be produced in React parent stacks either from\n  // host components or Server Components. We don't want to ignore those.\n  // This could hide user-space methods that are named like native JS methods but\n  // should you really do that?\n  return (\n    // e.g. JSON.parse\n    methodName.startsWith('JSON.') ||\n    // E.g. Promise.withResolves\n    methodName.startsWith('Function.') ||\n    // various JS built-ins\n    methodName.startsWith('Promise.') ||\n    methodName.startsWith('Array.') ||\n    methodName.startsWith('Set.') ||\n    methodName.startsWith('Map.')\n  )\n}\n\nexport function ignoreListAnonymousStackFramesIfSandwiched<Frame>(\n  frames: Frame[],\n  isAnonymousFrame: (frame: Frame) => boolean,\n  isIgnoredFrame: (frame: Frame) => boolean,\n  getMethodName: (frame: Frame) => string,\n  /** only passes frames for which `isAnonymousFrame` and their method is a native JS method or `isIgnoredFrame` return true */\n  ignoreFrame: (frame: Frame) => void\n): void {\n  for (let i = 1; i < frames.length; i++) {\n    const currentFrame = frames[i]\n    if (\n      !(\n        isAnonymousFrame(currentFrame) &&\n        isAnonymousFrameLikelyJSNative(getMethodName(currentFrame))\n      )\n    ) {\n      continue\n    }\n\n    const previousFrameIsIgnored = isIgnoredFrame(frames[i - 1])\n    if (previousFrameIsIgnored && i < frames.length - 1) {\n      let ignoreSandwich = false\n      let j = i + 1\n      for (j; j < frames.length; j++) {\n        const nextFrame = frames[j]\n        const nextFrameIsAnonymous =\n          isAnonymousFrame(nextFrame) &&\n          isAnonymousFrameLikelyJSNative(getMethodName(nextFrame))\n        if (nextFrameIsAnonymous) {\n          continue\n        }\n\n        const nextFrameIsIgnored = isIgnoredFrame(nextFrame)\n        if (nextFrameIsIgnored) {\n          ignoreSandwich = true\n          break\n        }\n      }\n\n      if (ignoreSandwich) {\n        for (i; i < j; i++) {\n          ignoreFrame(frames[i])\n        }\n      }\n    }\n  }\n}\n"], "names": ["devirtualizeReactServerURL", "filterStackFrameDEV", "findApplicableSourceMapPayload", "findSourceMapURLDEV", "ignoreListAnonymousStackFramesIfSandwiched", "sourceMapIgnoreListsEverything", "process", "noSourceMap", "undefined", "nativeFindSourceMap", "env", "NEXT_RUNTIME", "require", "findSourceMap", "sourceMap", "ignoreList", "sources", "length", "line0", "column0", "payload", "sections", "left", "right", "result", "middle", "section", "offset", "line", "column", "map", "didWarnAboutInvalidSourceMapDEV", "Set", "versions", "node", "startsWith", "sourceURL", "functionName", "line1", "column1", "includes", "sourceMapPayload", "cause", "NODE_ENV", "has", "add", "console", "error", "invalidSourceMap", "Symbol", "sourceMapURLs", "L<PERSON><PERSON><PERSON>", "url", "scriptNameOrSourceURL", "sourceMapURL", "get", "sourceMapJSON", "JSON", "stringify", "sourceMapURLData", "<PERSON><PERSON><PERSON>", "from", "toString", "set", "envIdx", "indexOf", "suffixIdx", "lastIndexOf", "decodeURI", "slice", "isAnonymousFrameLikelyJSNative", "methodName", "frames", "isAnonymousFrame", "isIgnoredFrame", "getMethodName", "ignore<PERSON><PERSON><PERSON>", "i", "currentFrame", "previousFrameIsIgnored", "ignoreSandwich", "j", "next<PERSON><PERSON><PERSON>", "nextFrameIsAnonymous", "nextFrameIsIgnored"], "mappings": ";;;;;;;;;;;;;;;;;;;IAkNgBA,0BAA0B;eAA1BA;;IAjGAC,mBAAmB;eAAnBA;;IAjDAC,8BAA8B;eAA9BA;;IAkHAC,mBAAmB;eAAnBA;;IA8DAC,0CAA0C;eAA1CA;;IA/LAC,8BAA8B;eAA9BA;;;0BAhDS;IA2GvBC;AAzGF,SAASC;IACP,OAAOC;AACT;AAEA,2CAA2C;AAC3C,MAAMC,sBACJH,QAAQI,GAAG,CAACC,YAAY,KAAK,SACzBJ,cACA,AAACK,QAAQ,UAAsCC,aAAa;AAsC3D,SAASR,+BACdS,SAAgC;IAEhC,OACEA,UAAUC,UAAU,KAAKP,aACzBM,UAAUE,OAAO,CAACC,MAAM,KAAKH,UAAUC,UAAU,CAACE,MAAM;AAE5D;AAQO,SAASf,+BACdgB,KAAa,EACbC,OAAe,EACfC,OAA+B;IAE/B,IAAI,cAAcA,SAAS;QACzB,IAAIA,QAAQC,QAAQ,CAACJ,MAAM,KAAK,GAAG;YACjC,OAAOT;QACT;QAEA,2FAA2F;QAC3F,uGAAuG;QACvG,MAAMa,WAAWD,QAAQC,QAAQ;QACjC,IAAIC,OAAO;QACX,IAAIC,QAAQF,SAASJ,MAAM,GAAG;QAC9B,IAAIO,SAAuC;QAE3C,MAAOF,QAAQC,MAAO;YACpB,kBAAkB;YAClB,MAAME,SAAS,CAAC,CAAE,CAAA,AAACH,CAAAA,OAAOC,KAAI,IAAK,CAAA;YACnC,MAAMG,UAAUL,QAAQ,CAACI,OAAO;YAChC,MAAME,SAASD,QAAQC,MAAM;YAE7B,IACEA,OAAOC,IAAI,GAAGV,SACbS,OAAOC,IAAI,KAAKV,SAASS,OAAOE,MAAM,IAAIV,SAC3C;gBACAK,SAASE;gBACTJ,OAAOG,SAAS;YAClB,OAAO;gBACLF,QAAQE,SAAS;YACnB;QACF;QAEA,OAAOD,WAAW,OAAOhB,YAAYgB,OAAOM,GAAG;IACjD,OAAO;QACL,OAAOV;IACT;AACF;AAEA,MAAMW,kCAAkC,IAAIC;AAE5C,MAAMnB,gBACJP,QAAQI,GAAG,CAACC,YAAY,KAAK,cAC7BL,yBAAAA,QAAQ2B,QAAQ,CAACC,IAAI,qBAArB5B,uBAAuB6B,UAAU,CAAC,SAE9B5B,cACAE;AAEC,SAASR,oBACdmC,SAAiB,EACjBC,YAAoB,EACpBC,KAAa,EACbC,OAAe;IAEf,IAAIH,cAAc,IAAI;QACpB,kEAAkE;QAClE,mEAAmE;QACnE,mEAAmE;QACnE,0EAA0E;QAC1E,iEAAiE;QACjE,OAAOC,iBAAiB;IAC1B;IACA,IAAID,UAAUD,UAAU,CAAC,YAAYC,UAAUI,QAAQ,CAAC,iBAAiB;QACvE,OAAO;IACT;IACA,IAAI;QACF,2DAA2D;QAC3D,sFAAsF;QACtF,mFAAmF;QACnF,MAAM1B,YAAYD,cAAcuB;QAChC,IAAItB,cAAcN,WAAW;YAC3B,4BAA4B;YAC5B,kFAAkF;YAClF,OAAO;QACT;QACA,MAAMiC,mBAAmBvC,+BACvBoC,QAAQ,GACRC,UAAU,GACVzB,UAAUM,OAAO;QAEnB,IAAIqB,qBAAqBjC,WAAW;YAClC,iDAAiD;YACjD,OAAO;QACT;QACA,OAAO,CAACH,+BAA+BoC;IACzC,EAAE,OAAOC,OAAO;QACd,IAAIpC,QAAQI,GAAG,CAACiC,QAAQ,KAAK,cAAc;YACzC,6CAA6C;YAC7C,IAAI,CAACZ,gCAAgCa,GAAG,CAACR,YAAY;gBACnDL,gCAAgCc,GAAG,CAACT;gBACpC,6EAA6E;gBAC7E,8EAA8E;gBAC9EU,QAAQC,KAAK,CACX,GAAGX,UAAU,6FAA6F,EAAEM,OAAO;YAEvH;QACF;QAEA,OAAO;IACT;AACF;AAEA,MAAMM,mBAAmBC,OAAO;AAChC,MAAMC,gBAAgB,IAAIC,kBAAQ,CAChC,MAAM,OAAO,MACb,CAACC,MACCA,QAAQJ,mBAEJ,+DAA+D;IAC/D,IAAI,OAEJI,IAAInC,MAAM;AAEX,SAASd,oBACdkD,qBAA6B;IAE7B,IAAIC,eAAeJ,cAAcK,GAAG,CAACF;IACrC,IAAIC,iBAAiB9C,WAAW;QAC9B,IAAIiC;QACJ,IAAI;gBACiB5B;YAAnB4B,oBAAmB5B,iBAAAA,cAAcwC,2CAAdxC,eAAsCO,OAAO;QAClE,EAAE,OAAOsB,OAAO;YACdI,QAAQC,KAAK,CACX,GAAGM,sBAAsB,gGAAgG,EAAEX,OAAO;QAEtI;QAEA,IAAID,qBAAqBjC,WAAW;YAClC8C,eAAeN;QACjB,OAAO;YACL,iFAAiF;YACjF,4EAA4E;YAC5E,MAAMQ,gBAAgBC,KAAKC,SAAS,CAACjB;YACrC,MAAMkB,mBAAmBC,OAAOC,IAAI,CAACL,eAAe,QAAQM,QAAQ,CAClE;YAEFR,eAAe,CAAC,6BAA6B,EAAEK,kBAAkB;QACnE;QAEAT,cAAca,GAAG,CAACV,uBAAuBC;IAC3C;IAEA,OAAOA,iBAAiBN,mBAAmB,OAAOM;AACpD;AAEO,SAAStD,2BAA2BoC,SAAiB;IAC1D,IAAIA,UAAUD,UAAU,CAAC,mBAAmB;QAC1C,iEAAiE;QACjE,MAAM6B,SAAS5B,UAAU6B,OAAO,CAAC,KAAK,iBAAiBhD,MAAM;QAC7D,MAAMiD,YAAY9B,UAAU+B,WAAW,CAAC;QACxC,IAAIH,SAAS,CAAC,KAAKE,YAAY,CAAC,GAAG;YACjC,OAAOE,UAAUhC,UAAUiC,KAAK,CAACL,SAAS,GAAGE;QAC/C;IACF;IACA,OAAO9B;AACT;AAEA,SAASkC,+BAA+BC,UAAkB;IACxD,2EAA2E;IAC3E,uEAAuE;IACvE,+EAA+E;IAC/E,6BAA6B;IAC7B,OACE,kBAAkB;IAClBA,WAAWpC,UAAU,CAAC,YACtB,4BAA4B;IAC5BoC,WAAWpC,UAAU,CAAC,gBACtB,uBAAuB;IACvBoC,WAAWpC,UAAU,CAAC,eACtBoC,WAAWpC,UAAU,CAAC,aACtBoC,WAAWpC,UAAU,CAAC,WACtBoC,WAAWpC,UAAU,CAAC;AAE1B;AAEO,SAAS/B,2CACdoE,MAAe,EACfC,gBAA2C,EAC3CC,cAAyC,EACzCC,aAAuC,EACvC,2HAA2H,GAC3HC,WAAmC;IAEnC,IAAK,IAAIC,IAAI,GAAGA,IAAIL,OAAOvD,MAAM,EAAE4D,IAAK;QACtC,MAAMC,eAAeN,MAAM,CAACK,EAAE;QAC9B,IACE,CACEJ,CAAAA,iBAAiBK,iBACjBR,+BAA+BK,cAAcG,cAAa,GAE5D;YACA;QACF;QAEA,MAAMC,yBAAyBL,eAAeF,MAAM,CAACK,IAAI,EAAE;QAC3D,IAAIE,0BAA0BF,IAAIL,OAAOvD,MAAM,GAAG,GAAG;YACnD,IAAI+D,iBAAiB;YACrB,IAAIC,IAAIJ,IAAI;YACZ,IAAKI,GAAGA,IAAIT,OAAOvD,MAAM,EAAEgE,IAAK;gBAC9B,MAAMC,YAAYV,MAAM,CAACS,EAAE;gBAC3B,MAAME,uBACJV,iBAAiBS,cACjBZ,+BAA+BK,cAAcO;gBAC/C,IAAIC,sBAAsB;oBACxB;gBACF;gBAEA,MAAMC,qBAAqBV,eAAeQ;gBAC1C,IAAIE,oBAAoB;oBACtBJ,iBAAiB;oBACjB;gBACF;YACF;YAEA,IAAIA,gBAAgB;gBAClB,IAAKH,GAAGA,IAAII,GAAGJ,IAAK;oBAClBD,YAAYJ,MAAM,CAACK,EAAE;gBACvB;YACF;QACF;IACF;AACF", "ignoreList": [0]}