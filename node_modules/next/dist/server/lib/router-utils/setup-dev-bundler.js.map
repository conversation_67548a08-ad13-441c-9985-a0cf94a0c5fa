{"version": 3, "sources": ["../../../../src/server/lib/router-utils/setup-dev-bundler.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../config-shared'\nimport type { FilesystemDynamicRoute } from './filesystem'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport {\n  getPageStaticInfo,\n  type MiddlewareMatcher,\n} from '../../../build/analysis/get-page-static-info'\nimport type { RoutesManifest } from '../../../build'\nimport type { MiddlewareRouteMatch } from '../../../shared/lib/router/utils/middleware-route-matcher'\nimport type { PropagateToWorkersField } from './types'\nimport type { NextJsHotReloaderInterface } from '../../dev/hot-reloader-types'\n\nimport { createDefineEnv } from '../../../build/swc'\nimport fs from 'fs'\nimport url from 'url'\nimport path from 'path'\nimport qs from 'querystring'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport { loadEnvConfig } from '@next/env'\nimport findUp from 'next/dist/compiled/find-up'\nimport { buildCustomRoute } from './filesystem'\nimport * as Log from '../../../build/output/log'\nimport HotReloaderWebpack from '../../dev/hot-reloader-webpack'\nimport { setGlobal } from '../../../trace/shared'\nimport type { Telemetry } from '../../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport loadJsConfig from '../../../build/load-jsconfig'\nimport { createValidFileMatcher } from '../find-page-file'\nimport {\n  EVENT_BUILD_FEATURE_USAGE,\n  eventCliSession,\n} from '../../../telemetry/events'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport {\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from '../../../build/entries'\nimport { verifyTypeScriptSetup } from '../../../lib/verify-typescript-setup'\nimport { verifyPartytownSetup } from '../../../lib/verify-partytown-setup'\nimport { getRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { normalizeAppPath } from '../../../shared/lib/router/utils/app-paths'\nimport { buildDataRoute } from './build-data-route'\nimport { getRouteMatcher } from '../../../shared/lib/router/utils/route-matcher'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { createClientRouterFilter } from '../../../lib/create-client-router-filter'\nimport { absolutePathToPage } from '../../../shared/lib/page-path/absolute-path-to-page'\nimport { generateInterceptionRoutesRewrites } from '../../../lib/generate-interception-routes-rewrites'\n\nimport {\n  CLIENT_STATIC_FILES_PATH,\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  PHASE_DEVELOPMENT_SERVER,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  ROUTES_MANIFEST,\n  PRERENDER_MANIFEST,\n} from '../../../shared/lib/constants'\n\nimport { getMiddlewareRouteMatcher } from '../../../shared/lib/router/utils/middleware-route-matcher'\n\nimport {\n  isMiddlewareFile,\n  NestedMiddlewareError,\n  isInstrumentationHookFile,\n  getPossibleMiddlewareFilenames,\n  getPossibleInstrumentationHookFilenames,\n} from '../../../build/utils'\nimport { devPageFiles } from '../../../build/webpack/plugins/next-types-plugin/shared'\nimport type { LazyRenderServerInstance } from '../router-server'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../dev/hot-reloader-types'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { createHotReloaderTurbopack } from '../../dev/hot-reloader-turbopack'\nimport { generateEncryptionKeyBase64 } from '../../app-render/encryption-utils-server'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { normalizeMetadataPageToRoute } from '../../../lib/metadata/get-metadata-route'\nimport { createEnvDefinitions } from '../experimental/create-env-definitions'\nimport { JsConfigPathsPlugin } from '../../../build/webpack/plugins/jsconfig-paths-plugin'\nimport { store as consoleStore } from '../../../build/output/store'\nimport {\n  isPersistentCachingEnabled,\n  ModuleBuildError,\n} from '../../../shared/lib/turbopack/utils'\nimport { getDefineEnv } from '../../../build/define-env'\nimport { TurbopackInternalError } from '../../../shared/lib/turbopack/internal-error'\nimport { normalizePath } from '../../../lib/normalize-path'\nimport { JSON_CONTENT_TYPE_HEADER } from '../../../lib/constants'\nimport {\n  createRouteTypesManifest,\n  writeRouteTypesManifest,\n  writeValidatorFile,\n} from './route-types-utils'\nimport { isParallelRouteSegment } from '../../../shared/lib/segment'\nimport { ensureLeadingSlash } from '../../../shared/lib/page-path/ensure-leading-slash'\n\nexport type SetupOpts = {\n  renderServer: LazyRenderServerInstance\n  dir: string\n  turbo?: boolean\n  appDir?: string\n  pagesDir?: string\n  telemetry: Telemetry\n  isCustomServer?: boolean\n  fsChecker: UnwrapPromise<\n    ReturnType<typeof import('./filesystem').setupFsCheck>\n  >\n  nextConfig: NextConfigComplete\n  port: number\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  resetFetch: () => void\n}\n\nexport interface DevRoutesManifest {\n  version: number\n  caseSensitive: RoutesManifest['caseSensitive']\n  basePath: RoutesManifest['basePath']\n  rewrites: RoutesManifest['rewrites']\n  redirects: RoutesManifest['redirects']\n  headers: RoutesManifest['headers']\n  i18n: RoutesManifest['i18n']\n  skipMiddlewareUrlNormalize: RoutesManifest['skipMiddlewareUrlNormalize']\n}\n\nexport type ServerFields = {\n  actualMiddlewareFile?: string | undefined\n  actualInstrumentationHookFile?: string | undefined\n  appPathRoutes?: Record<string, string | string[]>\n  middleware?:\n    | {\n        page: string\n        match: MiddlewareRouteMatch\n        matchers?: MiddlewareMatcher[]\n      }\n    | undefined\n  hasAppNotFound?: boolean\n  interceptionRoutes?: ReturnType<\n    typeof import('./filesystem').buildCustomRoute\n  >[]\n  setIsrStatus?: (key: string, value: boolean) => void\n  resetFetch?: () => void\n}\n\nasync function verifyTypeScript(opts: SetupOpts) {\n  let usingTypeScript = false\n  const verifyResult = await verifyTypeScriptSetup({\n    dir: opts.dir,\n    distDir: opts.nextConfig.distDir,\n    intentDirs: [opts.pagesDir, opts.appDir].filter(Boolean) as string[],\n    typeCheckPreflight: false,\n    tsconfigPath: opts.nextConfig.typescript.tsconfigPath,\n    disableStaticImages: opts.nextConfig.images.disableStaticImages,\n    hasAppDir: !!opts.appDir,\n    hasPagesDir: !!opts.pagesDir,\n  })\n\n  if (verifyResult.version) {\n    usingTypeScript = true\n  }\n  return usingTypeScript\n}\n\nexport async function propagateServerField(\n  opts: SetupOpts,\n  field: PropagateToWorkersField,\n  args: any\n) {\n  await opts.renderServer?.instance?.propagateServerField(opts.dir, field, args)\n}\n\nasync function startWatcher(\n  opts: SetupOpts & {\n    isSrcDir: boolean\n  }\n) {\n  const { nextConfig, appDir, pagesDir, dir, resetFetch } = opts\n  const { useFileSystemPublicRoutes } = nextConfig\n\n  const distDir = path.join(opts.dir, opts.nextConfig.distDir)\n\n  setGlobal('distDir', distDir)\n  setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n  const validFileMatcher = createValidFileMatcher(\n    nextConfig.pageExtensions,\n    appDir\n  )\n\n  const serverFields: ServerFields = {}\n\n  // Update logging state once based on next.config.js when initializing\n  consoleStore.setState({\n    logging: nextConfig.logging !== false,\n  })\n\n  const hotReloader: NextJsHotReloaderInterface = opts.turbo\n    ? await createHotReloaderTurbopack(opts, serverFields, distDir, resetFetch)\n    : new HotReloaderWebpack(opts.dir, {\n        isSrcDir: opts.isSrcDir,\n        appDir,\n        pagesDir,\n        distDir,\n        config: opts.nextConfig,\n        buildId: 'development',\n        encryptionKey: await generateEncryptionKeyBase64({\n          isBuild: false,\n          distDir,\n        }),\n        telemetry: opts.telemetry,\n        rewrites: opts.fsChecker.rewrites,\n        previewProps: opts.fsChecker.prerenderManifest.preview,\n        resetFetch,\n      })\n\n  await hotReloader.start()\n\n  // have to write this after starting hot-reloader since that\n  // cleans the dist dir\n  const distTypesDir = path.join(distDir, 'types')\n  await writeRouteTypesManifest(\n    {\n      appRoutes: {},\n      pageRoutes: {},\n      layoutRoutes: {},\n      appRouteHandlerRoutes: {},\n      redirectRoutes: {},\n      rewriteRoutes: {},\n      appPagePaths: new Set(),\n      pagesRouterPagePaths: new Set(),\n      layoutPaths: new Set(),\n      appRouteHandlers: new Set(),\n      pageApiRoutes: new Set(),\n      filePathToRoute: new Map(),\n    },\n    path.join(distTypesDir, 'routes.d.ts'),\n    opts.nextConfig\n  )\n\n  const usingTypeScript = await verifyTypeScript(opts)\n\n  const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n  const routesManifest: DevRoutesManifest = {\n    version: 3,\n    caseSensitive: !!nextConfig.experimental.caseSensitiveRoutes,\n    basePath: nextConfig.basePath,\n    rewrites: opts.fsChecker.rewrites,\n    redirects: opts.fsChecker.redirects,\n    headers: opts.fsChecker.headers,\n    i18n: nextConfig.i18n || undefined,\n    skipMiddlewareUrlNormalize: nextConfig.skipMiddlewareUrlNormalize,\n  }\n  await fs.promises.writeFile(\n    routesManifestPath,\n    JSON.stringify(routesManifest)\n  )\n\n  const prerenderManifestPath = path.join(distDir, PRERENDER_MANIFEST)\n  await fs.promises.writeFile(\n    prerenderManifestPath,\n    JSON.stringify(opts.fsChecker.prerenderManifest, null, 2)\n  )\n\n  if (opts.nextConfig.experimental.nextScriptWorkers) {\n    await verifyPartytownSetup(\n      opts.dir,\n      path.join(distDir, CLIENT_STATIC_FILES_PATH)\n    )\n  }\n\n  opts.fsChecker.ensureCallback(async function ensure(item) {\n    if (item.type === 'appFile' || item.type === 'pageFile') {\n      await hotReloader.ensurePage({\n        clientOnly: false,\n        page: item.itemPath,\n        isApp: item.type === 'appFile',\n        definition: undefined,\n      })\n    }\n  })\n\n  let resolved = false\n  let prevSortedRoutes: string[] = []\n\n  await new Promise<void>(async (resolve, reject) => {\n    if (pagesDir) {\n      // Watchpack doesn't emit an event for an empty directory\n      fs.readdir(pagesDir, (_, files) => {\n        if (files?.length) {\n          return\n        }\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      })\n    }\n\n    const pages = pagesDir ? [pagesDir] : []\n    const app = appDir ? [appDir] : []\n    const directories = [...pages, ...app]\n\n    const rootDir = pagesDir || appDir\n    const files = [\n      ...getPossibleMiddlewareFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n      ...getPossibleInstrumentationHookFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n    ]\n    let nestedMiddleware: string[] = []\n\n    const envFiles = [\n      '.env.development.local',\n      '.env.local',\n      '.env.development',\n      '.env',\n    ].map((file) => path.join(dir, file))\n\n    files.push(...envFiles)\n\n    // tsconfig/jsconfig paths hot-reloading\n    const tsconfigPaths = [\n      path.join(dir, 'tsconfig.json'),\n      path.join(dir, 'jsconfig.json'),\n    ] as const\n    files.push(...tsconfigPaths)\n\n    const wp = new Watchpack({\n      ignored: (pathname: string) => {\n        return (\n          !files.some((file) => file.startsWith(pathname)) &&\n          !directories.some(\n            (d) => pathname.startsWith(d) || d.startsWith(pathname)\n          )\n        )\n      },\n    })\n    const fileWatchTimes = new Map()\n    let enabledTypeScript = usingTypeScript\n    let previousClientRouterFilters: any\n    let previousConflictingPagePaths: Set<string> = new Set()\n\n    const routeTypesFilePath = path.join(distDir, 'types', 'routes.d.ts')\n    const validatorFilePath = path.join(distDir, 'types', 'validator.ts')\n\n    wp.on('aggregated', async () => {\n      let middlewareMatchers: MiddlewareMatcher[] | undefined\n      const routedPages: string[] = []\n      const knownFiles = wp.getTimeInfoEntries()\n      const appPaths: Record<string, string[]> = {}\n      const pageNameSet = new Set<string>()\n      const conflictingAppPagePaths = new Set<string>()\n      const appPageFilePaths = new Map<string, string>()\n      const pagesPageFilePaths = new Map<string, string>()\n      const appRouteHandlers: Array<{ route: string; filePath: string }> = []\n      const pageApiRoutes: Array<{ route: string; filePath: string }> = []\n\n      const pageRoutes: Array<{ route: string; filePath: string }> = []\n      const appRoutes: Array<{ route: string; filePath: string }> = []\n      const layoutRoutes: Array<{ route: string; filePath: string }> = []\n      const slots: Array<{ name: string; parent: string }> = []\n\n      let envChange = false\n      let tsconfigChange = false\n      let conflictingPageChange = 0\n      let hasRootAppNotFound = false\n\n      const { appFiles, pageFiles } = opts.fsChecker\n\n      appFiles.clear()\n      pageFiles.clear()\n      devPageFiles.clear()\n\n      const sortedKnownFiles: string[] = [...knownFiles.keys()].sort(\n        sortByPageExts(nextConfig.pageExtensions)\n      )\n\n      for (const fileName of sortedKnownFiles) {\n        if (\n          !files.includes(fileName) &&\n          !directories.some((d) => fileName.startsWith(d))\n        ) {\n          continue\n        }\n        const meta = knownFiles.get(fileName)\n\n        const watchTime = fileWatchTimes.get(fileName)\n        // If the file is showing up for the first time or the meta.timestamp is changed since last time\n        const watchTimeChange =\n          watchTime === undefined ||\n          (watchTime && watchTime !== meta?.timestamp)\n        fileWatchTimes.set(fileName, meta?.timestamp)\n\n        if (envFiles.includes(fileName)) {\n          if (watchTimeChange) {\n            envChange = true\n          }\n          continue\n        }\n\n        if (tsconfigPaths.includes(fileName)) {\n          if (fileName.endsWith('tsconfig.json')) {\n            enabledTypeScript = true\n          }\n          if (watchTimeChange) {\n            tsconfigChange = true\n          }\n          continue\n        }\n\n        if (\n          meta?.accuracy === undefined ||\n          !validFileMatcher.isPageFile(fileName)\n        ) {\n          continue\n        }\n\n        const isAppPath = Boolean(\n          appDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(appDir) + '/'\n            )\n        )\n        const isPagePath = Boolean(\n          pagesDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(pagesDir) + '/'\n            )\n        )\n\n        const rootFile = absolutePathToPage(fileName, {\n          dir: dir,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: false,\n          pagesType: PAGE_TYPES.ROOT,\n        })\n\n        if (isMiddlewareFile(rootFile)) {\n          const staticInfo = await getStaticInfoIncludingLayouts({\n            pageFilePath: fileName,\n            config: nextConfig,\n            appDir: appDir,\n            page: rootFile,\n            isDev: true,\n            isInsideAppDir: isAppPath,\n            pageExtensions: nextConfig.pageExtensions,\n          })\n          if (nextConfig.output === 'export') {\n            Log.error(\n              'Middleware cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n            continue\n          }\n          serverFields.actualMiddlewareFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualMiddlewareFile',\n            serverFields.actualMiddlewareFile\n          )\n          middlewareMatchers = staticInfo.middleware?.matchers || [\n            { regexp: '.*', originalSource: '/:path*' },\n          ]\n          continue\n        }\n        if (isInstrumentationHookFile(rootFile)) {\n          serverFields.actualInstrumentationHookFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualInstrumentationHookFile',\n            serverFields.actualInstrumentationHookFile\n          )\n          continue\n        }\n\n        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {\n          enabledTypeScript = true\n        }\n\n        if (!(isAppPath || isPagePath)) {\n          continue\n        }\n\n        // Collect all current filenames for the TS plugin to use\n        devPageFiles.add(fileName)\n\n        let pageName = absolutePathToPage(fileName, {\n          dir: isAppPath ? appDir! : pagesDir!,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: isAppPath,\n          pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n        })\n\n        if (\n          isAppPath &&\n          appDir &&\n          isMetadataRouteFile(\n            fileName.replace(appDir, ''),\n            nextConfig.pageExtensions,\n            true\n          )\n        ) {\n          const staticInfo = await getPageStaticInfo({\n            pageFilePath: fileName,\n            nextConfig: {},\n            page: pageName,\n            isDev: true,\n            pageType: PAGE_TYPES.APP,\n          })\n\n          pageName = normalizeMetadataPageToRoute(\n            pageName,\n            !!(staticInfo.generateSitemaps || staticInfo.generateImageMetadata)\n          )\n        }\n\n        if (\n          !isAppPath &&\n          pageName.startsWith('/api/') &&\n          nextConfig.output === 'export'\n        ) {\n          Log.error(\n            'API Routes cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n          )\n          continue\n        }\n\n        if (isAppPath) {\n          const isRootNotFound = validFileMatcher.isRootNotFound(fileName)\n          hasRootAppNotFound = true\n\n          if (isRootNotFound) {\n            continue\n          }\n\n          // Ignore files/directories starting with `_` in the app directory\n          if (normalizePathSep(pageName).includes('/_')) {\n            continue\n          }\n\n          // Record parallel route slots for layout typing\n          // May run multiple times (e.g. if a parallel route\n          // has both a layout and a page, and children) but that's fine\n          const segments = normalizePathSep(pageName).split('/')\n          for (let i = segments.length - 1; i >= 0; i--) {\n            const segment = segments[i]\n            if (isParallelRouteSegment(segment)) {\n              const parentPath = normalizeAppPath(\n                segments.slice(0, i).join('/')\n              )\n\n              const slotName = segment.slice(1)\n              // check if the slot already exists\n              if (\n                slots.some(\n                  (s) => s.name === slotName && s.parent === parentPath\n                )\n              )\n                continue\n\n              slots.push({\n                name: slotName,\n                parent: parentPath,\n              })\n              break\n            }\n          }\n\n          // Record layouts\n          if (validFileMatcher.isAppLayoutPage(fileName)) {\n            layoutRoutes.push({\n              route: ensureLeadingSlash(\n                normalizeAppPath(normalizePathSep(pageName)).replace(\n                  /\\/layout$/,\n                  ''\n                )\n              ),\n              filePath: fileName,\n            })\n          }\n\n          if (!validFileMatcher.isAppRouterPage(fileName)) {\n            continue\n          }\n\n          const originalPageName = pageName\n          pageName = normalizeAppPath(pageName).replace(/%5F/g, '_')\n          if (!appPaths[pageName]) {\n            appPaths[pageName] = []\n          }\n          appPaths[pageName].push(\n            opts.turbo\n              ? // Turbopack outputs the correct path which is normalized with the `_`.\n                originalPageName.replace(/%5F/g, '_')\n              : originalPageName\n          )\n\n          if (useFileSystemPublicRoutes) {\n            appFiles.add(pageName)\n          }\n\n          if (validFileMatcher.isAppRouterRoute(fileName)) {\n            appRouteHandlers.push({\n              route: normalizePathSep(pageName),\n              filePath: fileName,\n            })\n          } else {\n            appRoutes.push({\n              route: normalizePathSep(pageName),\n              filePath: fileName,\n            })\n          }\n\n          if (routedPages.includes(pageName)) {\n            continue\n          }\n        } else {\n          if (useFileSystemPublicRoutes) {\n            pageFiles.add(pageName)\n            // always add to nextDataRoutes for now but in future only add\n            // entries that actually use getStaticProps/getServerSideProps\n            opts.fsChecker.nextDataRoutes.add(pageName)\n          }\n\n          if (pageName.startsWith('/api/')) {\n            pageApiRoutes.push({\n              route: normalizePathSep(pageName),\n              filePath: fileName,\n            })\n          } else {\n            pageRoutes.push({\n              route: normalizePathSep(pageName),\n              filePath: fileName,\n            })\n          }\n        }\n\n        // Record pages\n        if (isAppPath) {\n          appPageFilePaths.set(pageName, fileName)\n        } else {\n          pagesPageFilePaths.set(pageName, fileName)\n        }\n\n        if (appDir && pageNameSet.has(pageName)) {\n          conflictingAppPagePaths.add(pageName)\n        } else {\n          pageNameSet.add(pageName)\n        }\n\n        /**\n         * If there is a middleware that is not declared in the root we will\n         * warn without adding it so it doesn't make its way into the system.\n         */\n        if (/[\\\\\\\\/]_middleware$/.test(pageName)) {\n          nestedMiddleware.push(pageName)\n          continue\n        }\n\n        routedPages.push(pageName)\n      }\n\n      const numConflicting = conflictingAppPagePaths.size\n      conflictingPageChange = numConflicting - previousConflictingPagePaths.size\n\n      if (conflictingPageChange !== 0) {\n        if (numConflicting > 0) {\n          let errorMessage = `Conflicting app and page file${\n            numConflicting === 1 ? ' was' : 's were'\n          } found, please remove the conflicting files to continue:\\n`\n\n          for (const p of conflictingAppPagePaths) {\n            const appPath = path.relative(dir, appPageFilePaths.get(p)!)\n            const pagesPath = path.relative(dir, pagesPageFilePaths.get(p)!)\n            errorMessage += `  \"${pagesPath}\" - \"${appPath}\"\\n`\n          }\n          hotReloader.setHmrServerError(new Error(errorMessage))\n        } else if (numConflicting === 0) {\n          hotReloader.clearHmrServerError()\n          await propagateServerField(opts, 'reloadMatchers', undefined)\n        }\n      }\n\n      previousConflictingPagePaths = conflictingAppPagePaths\n\n      let clientRouterFilters: any\n      if (nextConfig.experimental.clientRouterFilter) {\n        clientRouterFilters = createClientRouterFilter(\n          Object.keys(appPaths),\n          nextConfig.experimental.clientRouterFilterRedirects\n            ? ((nextConfig as any)._originalRedirects || []).filter(\n                (r: any) => !r.internal\n              )\n            : [],\n          nextConfig.experimental.clientRouterFilterAllowedRate\n        )\n\n        if (\n          !previousClientRouterFilters ||\n          JSON.stringify(previousClientRouterFilters) !==\n            JSON.stringify(clientRouterFilters)\n        ) {\n          envChange = true\n          previousClientRouterFilters = clientRouterFilters\n        }\n      }\n\n      if (!usingTypeScript && enabledTypeScript) {\n        // we tolerate the error here as this is best effort\n        // and the manual install command will be shown\n        await verifyTypeScript(opts)\n          .then(() => {\n            tsconfigChange = true\n          })\n          .catch(() => {})\n      }\n\n      if (envChange || tsconfigChange) {\n        if (envChange) {\n          const { loadedEnvFiles } = loadEnvConfig(\n            dir,\n            process.env.NODE_ENV === 'development',\n            Log,\n            true,\n            (envFilePath) => {\n              Log.info(`Reload env: ${envFilePath}`)\n            }\n          )\n\n          if (usingTypeScript && nextConfig.experimental?.typedEnv) {\n            // do not await, this is not essential for further process\n            createEnvDefinitions({\n              distDir,\n              loadedEnvFiles: [\n                ...loadedEnvFiles,\n                {\n                  path: nextConfig.configFileName,\n                  env: nextConfig.env,\n                  contents: '',\n                },\n              ],\n            })\n          }\n\n          await propagateServerField(opts, 'loadEnvConfig', [\n            { dev: true, forceReload: true, silent: true },\n          ])\n        }\n        let tsconfigResult:\n          | UnwrapPromise<ReturnType<typeof loadJsConfig>>\n          | undefined\n\n        if (tsconfigChange) {\n          try {\n            tsconfigResult = await loadJsConfig(dir, nextConfig)\n          } catch (_) {\n            /* do we want to log if there are syntax errors in tsconfig while editing? */\n          }\n        }\n\n        if (hotReloader.turbopackProject) {\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          const rootPath =\n            opts.nextConfig.turbopack?.root ||\n            opts.nextConfig.outputFileTracingRoot ||\n            opts.dir\n          await hotReloader.turbopackProject.update({\n            defineEnv: createDefineEnv({\n              isTurbopack: true,\n              clientRouterFilters,\n              config: nextConfig,\n              dev: true,\n              distDir,\n              fetchCacheKeyPrefix:\n                opts.nextConfig.experimental.fetchCacheKeyPrefix,\n              hasRewrites,\n              // TODO: Implement\n              middlewareMatchers: undefined,\n              projectPath: opts.dir,\n              rewrites: opts.fsChecker.rewrites,\n            }),\n            rootPath,\n            projectPath: normalizePath(path.relative(rootPath, dir)),\n          })\n        }\n\n        hotReloader.activeWebpackConfigs?.forEach((config, idx) => {\n          const isClient = idx === 0\n          const isNodeServer = idx === 1\n          const isEdgeServer = idx === 2\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          if (tsconfigChange) {\n            config.resolve?.plugins?.forEach((plugin: any) => {\n              // look for the JsConfigPathsPlugin and update with\n              // the latest paths/baseUrl config\n              if (plugin instanceof JsConfigPathsPlugin && tsconfigResult) {\n                const { resolvedBaseUrl, jsConfig } = tsconfigResult\n                const currentResolvedBaseUrl = plugin.resolvedBaseUrl\n                const resolvedUrlIndex = config.resolve?.modules?.findIndex(\n                  (item) => item === currentResolvedBaseUrl?.baseUrl\n                )\n\n                if (resolvedBaseUrl) {\n                  if (\n                    resolvedBaseUrl.baseUrl !== currentResolvedBaseUrl?.baseUrl\n                  ) {\n                    // remove old baseUrl and add new one\n                    if (resolvedUrlIndex && resolvedUrlIndex > -1) {\n                      config.resolve?.modules?.splice(resolvedUrlIndex, 1)\n                    }\n\n                    // If the resolvedBaseUrl is implicit we only remove the previous value.\n                    // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n                    if (!resolvedBaseUrl.isImplicit) {\n                      config.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n                    }\n                  }\n                }\n\n                if (jsConfig?.compilerOptions?.paths && resolvedBaseUrl) {\n                  Object.keys(plugin.paths).forEach((key) => {\n                    delete plugin.paths[key]\n                  })\n                  Object.assign(plugin.paths, jsConfig.compilerOptions.paths)\n                  plugin.resolvedBaseUrl = resolvedBaseUrl\n                }\n              }\n            })\n          }\n\n          if (envChange) {\n            config.plugins?.forEach((plugin: any) => {\n              // we look for the DefinePlugin definitions so we can\n              // update them on the active compilers\n              if (\n                plugin &&\n                typeof plugin.definitions === 'object' &&\n                plugin.definitions.__NEXT_DEFINE_ENV\n              ) {\n                const newDefine = getDefineEnv({\n                  isTurbopack: false,\n                  clientRouterFilters,\n                  config: nextConfig,\n                  dev: true,\n                  distDir,\n                  fetchCacheKeyPrefix:\n                    opts.nextConfig.experimental.fetchCacheKeyPrefix,\n                  hasRewrites,\n                  isClient,\n                  isEdgeServer,\n                  isNodeServer,\n                  middlewareMatchers: undefined,\n                  projectPath: opts.dir,\n                  rewrites: opts.fsChecker.rewrites,\n                })\n\n                Object.keys(plugin.definitions).forEach((key) => {\n                  if (!(key in newDefine)) {\n                    delete plugin.definitions[key]\n                  }\n                })\n                Object.assign(plugin.definitions, newDefine)\n              }\n            })\n          }\n        })\n        await hotReloader.invalidate({\n          reloadAfterInvalidation: envChange,\n        })\n      }\n\n      if (nestedMiddleware.length > 0) {\n        Log.error(\n          new NestedMiddlewareError(\n            nestedMiddleware,\n            dir,\n            (pagesDir || appDir)!\n          ).message\n        )\n        nestedMiddleware = []\n      }\n\n      // Make sure to sort parallel routes to make the result deterministic.\n      serverFields.appPathRoutes = Object.fromEntries(\n        Object.entries(appPaths).map(([k, v]) => [k, v.sort()])\n      )\n      await propagateServerField(\n        opts,\n        'appPathRoutes',\n        serverFields.appPathRoutes\n      )\n\n      // TODO: pass this to fsChecker/next-dev-server?\n      serverFields.middleware = middlewareMatchers\n        ? {\n            match: null as any,\n            page: '/',\n            matchers: middlewareMatchers,\n          }\n        : undefined\n\n      await propagateServerField(opts, 'middleware', serverFields.middleware)\n      serverFields.hasAppNotFound = hasRootAppNotFound\n\n      opts.fsChecker.middlewareMatcher = serverFields.middleware?.matchers\n        ? getMiddlewareRouteMatcher(serverFields.middleware?.matchers)\n        : undefined\n\n      const interceptionRoutes = generateInterceptionRoutesRewrites(\n        Object.keys(appPaths),\n        opts.nextConfig.basePath\n      ).map((item) =>\n        buildCustomRoute(\n          'before_files_rewrite',\n          item,\n          opts.nextConfig.basePath,\n          opts.nextConfig.experimental.caseSensitiveRoutes\n        )\n      )\n\n      opts.fsChecker.rewrites.beforeFiles.push(...interceptionRoutes)\n\n      const exportPathMap =\n        (typeof nextConfig.exportPathMap === 'function' &&\n          (await nextConfig.exportPathMap?.(\n            {},\n            {\n              dev: true,\n              dir: opts.dir,\n              outDir: null,\n              distDir: distDir,\n              buildId: 'development',\n            }\n          ))) ||\n        {}\n\n      const exportPathMapEntries = Object.entries(exportPathMap || {})\n\n      if (exportPathMapEntries.length > 0) {\n        opts.fsChecker.exportPathMapRoutes = exportPathMapEntries.map(\n          ([key, value]) =>\n            buildCustomRoute(\n              'before_files_rewrite',\n              {\n                source: key,\n                destination: `${value.page}${\n                  value.query ? '?' : ''\n                }${qs.stringify(value.query)}`,\n              },\n              opts.nextConfig.basePath,\n              opts.nextConfig.experimental.caseSensitiveRoutes\n            )\n        )\n      }\n\n      try {\n        // we serve a separate manifest with all pages for the client in\n        // dev mode so that we can match a page after a rewrite on the client\n        // before it has been built and is populated in the _buildManifest\n        const sortedRoutes = getSortedRoutes(routedPages)\n\n        opts.fsChecker.dynamicRoutes = sortedRoutes.map(\n          (page): FilesystemDynamicRoute => {\n            const regex = getRouteRegex(page)\n            return {\n              regex: regex.re.toString(),\n              match: getRouteMatcher(regex),\n              page,\n            }\n          }\n        )\n\n        const dataRoutes: typeof opts.fsChecker.dynamicRoutes = []\n\n        for (const page of sortedRoutes) {\n          const route = buildDataRoute(page, 'development')\n          const routeRegex = getRouteRegex(route.page)\n          dataRoutes.push({\n            ...route,\n            regex: routeRegex.re.toString(),\n            match: getRouteMatcher({\n              // TODO: fix this in the manifest itself, must also be fixed in\n              // upstream builder that relies on this\n              re: opts.nextConfig.i18n\n                ? new RegExp(\n                    route.dataRouteRegex.replace(\n                      `/development/`,\n                      `/development/(?<nextLocale>[^/]+?)/`\n                    )\n                  )\n                : new RegExp(route.dataRouteRegex),\n              groups: routeRegex.groups,\n            }),\n          })\n        }\n        opts.fsChecker.dynamicRoutes.unshift(...dataRoutes)\n\n        if (!prevSortedRoutes?.every((val, idx) => val === sortedRoutes[idx])) {\n          const addedRoutes = sortedRoutes.filter(\n            (route) => !prevSortedRoutes.includes(route)\n          )\n          const removedRoutes = prevSortedRoutes.filter(\n            (route) => !sortedRoutes.includes(route)\n          )\n\n          // emit the change so clients fetch the update\n          hotReloader.send({\n            action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE,\n            data: [\n              {\n                devPagesManifest: true,\n              },\n            ],\n          })\n\n          addedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n              data: [route],\n            })\n          })\n\n          removedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n              data: [route],\n            })\n          })\n        }\n        prevSortedRoutes = sortedRoutes\n\n        if (usingTypeScript) {\n          const routeTypesManifest = await createRouteTypesManifest({\n            dir,\n            pageRoutes,\n            appRoutes,\n            layoutRoutes,\n            slots,\n            redirects: opts.nextConfig.redirects,\n            rewrites: opts.nextConfig.rewrites,\n            // Ensure relative paths in validator.ts are computed from validatorFilePath,\n            // matching behavior of build and CLI typegen.\n            validatorFilePath,\n            appRouteHandlers,\n            pageApiRoutes,\n          })\n\n          await writeRouteTypesManifest(\n            routeTypesManifest,\n            routeTypesFilePath,\n            opts.nextConfig\n          )\n          await writeValidatorFile(routeTypesManifest, validatorFilePath)\n        }\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      } catch (e) {\n        if (!resolved) {\n          reject(e)\n          resolved = true\n        } else {\n          Log.warn('Failed to reload dynamic routes:', e)\n        }\n      } finally {\n        // Reload the matchers. The filesystem would have been written to,\n        // and the matchers need to re-scan it to update the router.\n        await propagateServerField(opts, 'reloadMatchers', undefined)\n      }\n    })\n\n    wp.watch({ directories: [dir], startTime: 0 })\n  })\n\n  const clientPagesManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_PAGES_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(clientPagesManifestPath)\n\n  const devMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devMiddlewareManifestPath)\n\n  const devTurbopackMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devTurbopackMiddlewareManifestPath)\n\n  async function requestHandler(req: IncomingMessage, res: ServerResponse) {\n    const parsedUrl = url.parse(req.url || '/')\n\n    if (parsedUrl.pathname?.includes(clientPagesManifestPath)) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', JSON_CONTENT_TYPE_HEADER)\n      res.end(\n        JSON.stringify({\n          pages: prevSortedRoutes.filter(\n            (route) => !opts.fsChecker.appFiles.has(route)\n          ),\n        })\n      )\n      return { finished: true }\n    }\n\n    if (\n      parsedUrl.pathname?.includes(devMiddlewareManifestPath) ||\n      parsedUrl.pathname?.includes(devTurbopackMiddlewareManifestPath)\n    ) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', JSON_CONTENT_TYPE_HEADER)\n      res.end(JSON.stringify(serverFields.middleware?.matchers || []))\n      return { finished: true }\n    }\n    return { finished: false }\n  }\n\n  function logErrorWithOriginalStack(\n    err: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ) {\n    if (err instanceof ModuleBuildError) {\n      // Errors that may come from issues from the user's code\n      Log.error(err.message)\n    } else if (err instanceof TurbopackInternalError) {\n      // An internal Turbopack error that has been handled by next-swc, written\n      // to disk and a simplified message shown to user on the Rust side.\n    } else if (type === 'warning') {\n      Log.warn(err)\n    } else if (type === 'app-dir') {\n      Log.error(err)\n    } else if (type) {\n      Log.error(`${type}:`, err)\n    } else {\n      Log.error(err)\n    }\n  }\n\n  return {\n    serverFields,\n    hotReloader,\n    requestHandler,\n    logErrorWithOriginalStack,\n\n    async ensureMiddleware(requestUrl?: string) {\n      if (!serverFields.actualMiddlewareFile) return\n      return hotReloader.ensurePage({\n        page: serverFields.actualMiddlewareFile,\n        clientOnly: false,\n        definition: undefined,\n        url: requestUrl,\n      })\n    },\n  }\n}\n\nexport async function setupDevBundler(opts: SetupOpts) {\n  const isSrcDir = path\n    .relative(opts.dir, opts.pagesDir || opts.appDir || '')\n    .startsWith('src')\n\n  const result = await startWatcher({\n    ...opts,\n    isSrcDir,\n  })\n\n  opts.telemetry.record(\n    eventCliSession(\n      path.join(opts.dir, opts.nextConfig.distDir),\n      opts.nextConfig,\n      {\n        webpackVersion: 5,\n        isSrcDir,\n        turboFlag: !!opts.turbo,\n        cliCommand: 'dev',\n        appDir: !!opts.appDir,\n        pagesDir: !!opts.pagesDir,\n        isCustomServer: !!opts.isCustomServer,\n        hasNowJson: !!(await findUp('now.json', { cwd: opts.dir })),\n      }\n    )\n  )\n\n  // Track build features for dev server here:\n  opts.telemetry.record({\n    eventName: EVENT_BUILD_FEATURE_USAGE,\n    payload: {\n      featureName: 'turbopackPersistentCaching',\n      invocationCount: isPersistentCachingEnabled(opts.nextConfig) ? 1 : 0,\n    },\n  })\n\n  return result\n}\n\nexport type DevBundler = Awaited<ReturnType<typeof setupDevBundler>>\n\n// Returns a trace rewritten through Turbopack's sourcemaps\n"], "names": ["propagateServerField", "setupDevBundler", "verifyTypeScript", "opts", "usingTypeScript", "verifyResult", "verifyTypeScriptSetup", "dir", "distDir", "nextConfig", "intentDirs", "pagesDir", "appDir", "filter", "Boolean", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "version", "field", "args", "renderServer", "instance", "startWatcher", "resetFetch", "useFileSystemPublicRoutes", "path", "join", "setGlobal", "PHASE_DEVELOPMENT_SERVER", "validFile<PERSON><PERSON><PERSON>", "createValidFileMatcher", "pageExtensions", "serverFields", "consoleStore", "setState", "logging", "hotReloader", "turbo", "createHotReloaderTurbopack", "HotReloaderWebpack", "isSrcDir", "config", "buildId", "<PERSON><PERSON><PERSON>", "generateEncryptionKeyBase64", "isBuild", "telemetry", "rewrites", "fs<PERSON><PERSON><PERSON>", "previewProps", "prerenderManifest", "preview", "start", "distTypesDir", "writeRouteTypesManifest", "appRoutes", "pageRoutes", "layoutRoutes", "appRouteHandlerRoutes", "redirectRoutes", "rewriteRoutes", "appPagePaths", "Set", "pagesRouterPagePaths", "layoutPaths", "appRouteHandlers", "pageApiRoutes", "filePathToRoute", "Map", "routesManifestPath", "ROUTES_MANIFEST", "routesManifest", "caseSensitive", "experimental", "caseSensitiveRoutes", "basePath", "redirects", "headers", "i18n", "undefined", "skipMiddlewareUrlNormalize", "fs", "promises", "writeFile", "JSON", "stringify", "prerenderManifestPath", "PRERENDER_MANIFEST", "nextScriptWorkers", "verifyPartytownSetup", "CLIENT_STATIC_FILES_PATH", "ensure<PERSON><PERSON>back", "ensure", "item", "type", "ensurePage", "clientOnly", "page", "itemPath", "isApp", "definition", "resolved", "prevSortedRoutes", "Promise", "resolve", "reject", "readdir", "_", "files", "length", "pages", "app", "directories", "rootDir", "getPossibleMiddlewareFilenames", "getPossibleInstrumentationHookFilenames", "nestedMiddleware", "envFiles", "map", "file", "push", "tsconfigPaths", "wp", "Watchpack", "ignored", "pathname", "some", "startsWith", "d", "fileWatchTimes", "enabledTypeScript", "previousClientRouterFilters", "previousConflictingPagePaths", "routeTypesFilePath", "validatorFilePath", "on", "middlewareMatchers", "routedPages", "knownFiles", "getTimeInfoEntries", "appPaths", "pageNameSet", "conflictingAppPagePaths", "appPageFilePaths", "pagesPageFilePaths", "slots", "envChange", "tsconfigChange", "conflictingPageChange", "hasRootAppNotFound", "appFiles", "pageFiles", "clear", "devPageFiles", "sortedKnownFiles", "keys", "sort", "sortByPageExts", "fileName", "includes", "meta", "get", "watchTime", "watchTimeChange", "timestamp", "set", "endsWith", "accuracy", "isPageFile", "isAppPath", "normalizePathSep", "isPagePath", "rootFile", "absolutePathToPage", "extensions", "keepIndex", "pagesType", "PAGE_TYPES", "ROOT", "isMiddlewareFile", "staticInfo", "getStaticInfoIncludingLayouts", "pageFilePath", "isDev", "isInsideAppDir", "output", "Log", "error", "actualMiddlewareFile", "middleware", "matchers", "regexp", "originalSource", "isInstrumentationHookFile", "actualInstrumentationHookFile", "add", "pageName", "APP", "PAGES", "isMetadataRouteFile", "replace", "getPageStaticInfo", "pageType", "normalizeMetadataPageToRoute", "generateSitemaps", "generateImageMetadata", "isRootNotFound", "segments", "split", "i", "segment", "isParallelRouteSegment", "parentPath", "normalizeAppPath", "slice", "slotName", "s", "name", "parent", "isAppLayoutPage", "route", "ensureLeadingSlash", "filePath", "isAppRouterPage", "originalPageName", "isAppRouterRoute", "nextDataRoutes", "has", "test", "numConflicting", "size", "errorMessage", "p", "appPath", "relative", "pagesPath", "setHmrServerError", "Error", "clearHmrServerError", "clientRouterFilters", "clientRouterFilter", "createClientRouterFilter", "Object", "clientRouterFilterRedirects", "_originalRedirects", "r", "internal", "clientRouterFilterAllowedRate", "then", "catch", "loadedEnvFiles", "loadEnvConfig", "process", "env", "NODE_ENV", "env<PERSON><PERSON><PERSON><PERSON>", "info", "typedEnv", "createEnvDefinitions", "configFileName", "contents", "dev", "forceReload", "silent", "tsconfigResult", "loadJsConfig", "turbopackProject", "hasRewrites", "afterFiles", "beforeFiles", "fallback", "rootPath", "turbopack", "root", "outputFileTracingRoot", "update", "defineEnv", "createDefineEnv", "isTurbopack", "fetchCacheKeyPrefix", "projectPath", "normalizePath", "activeWebpackConfigs", "for<PERSON>ach", "idx", "isClient", "isNodeServer", "isEdgeServer", "plugins", "plugin", "JsConfigPathsPlugin", "jsConfig", "resolvedBaseUrl", "currentResolvedBaseUrl", "resolvedUrlIndex", "modules", "findIndex", "baseUrl", "splice", "isImplicit", "compilerOptions", "paths", "key", "assign", "definitions", "__NEXT_DEFINE_ENV", "newDefine", "getDefineEnv", "invalidate", "reloadAfterInvalidation", "NestedMiddlewareError", "message", "appPathRoutes", "fromEntries", "entries", "k", "v", "match", "hasAppNotFound", "middlewareMatcher", "getMiddlewareRouteMatcher", "interceptionRoutes", "generateInterceptionRoutesRewrites", "buildCustomRoute", "exportPathMap", "outDir", "exportPathMapEntries", "exportPathMapRoutes", "value", "source", "destination", "query", "qs", "sortedRoutes", "getSortedRoutes", "dynamicRoutes", "regex", "getRouteRegex", "re", "toString", "getRouteMatcher", "dataRoutes", "buildDataRoute", "routeRegex", "RegExp", "dataRouteRegex", "groups", "unshift", "every", "val", "addedRoutes", "removedRoutes", "send", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "DEV_PAGES_MANIFEST_UPDATE", "data", "devPagesManifest", "ADDED_PAGE", "REMOVED_PAGE", "routeTypesManifest", "createRouteTypesManifest", "writeValidatorFile", "e", "warn", "watch", "startTime", "clientPagesManifestPath", "DEV_CLIENT_PAGES_MANIFEST", "devVirtualFsItems", "devMiddlewareManifestPath", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "devTurbopackMiddlewareManifestPath", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "requestHandler", "req", "res", "parsedUrl", "url", "parse", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "JSON_CONTENT_TYPE_HEADER", "end", "finished", "logErrorWithOriginalStack", "err", "ModuleBuildError", "TurbopackInternalError", "ensureMiddleware", "requestUrl", "result", "record", "eventCliSession", "webpackVersion", "turboFlag", "cliCommand", "isCustomServer", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "eventName", "EVENT_BUILD_FEATURE_USAGE", "payload", "featureName", "invocationCount", "isPersistentCachingEnabled"], "mappings": ";;;;;;;;;;;;;;;IAgKsBA,oBAAoB;eAApBA;;IAy+BAC,eAAe;eAAfA;;;mCAnoCf;qBAMyB;2DACjB;4DACC;6DACC;oEACF;kEACO;qBACQ;+DACX;4BACc;6DACZ;2EACU;wBACL;qEAGD;8BACc;wBAIhC;uBACyB;yBAIzB;uCAC+B;sCACD;4BACP;0BACG;gCACF;8BACC;kCACC;0CACQ;oCACN;oDACgB;2BAU5C;wCAEmC;wBAQnC;yBACsB;kCAEe;2BACjB;sCACgB;uCACC;iCACR;kCACS;sCACR;qCACD;uBACE;wBAI/B;2BACsB;+BACU;+BACT;4BACW;iCAKlC;yBACgC;oCACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDnC,eAAeC,iBAAiBC,IAAe;IAC7C,IAAIC,kBAAkB;IACtB,MAAMC,eAAe,MAAMC,IAAAA,4CAAqB,EAAC;QAC/CC,KAAKJ,KAAKI,GAAG;QACbC,SAASL,KAAKM,UAAU,CAACD,OAAO;QAChCE,YAAY;YAACP,KAAKQ,QAAQ;YAAER,KAAKS,MAAM;SAAC,CAACC,MAAM,CAACC;QAChDC,oBAAoB;QACpBC,cAAcb,KAAKM,UAAU,CAACQ,UAAU,CAACD,YAAY;QACrDE,qBAAqBf,KAAKM,UAAU,CAACU,MAAM,CAACD,mBAAmB;QAC/DE,WAAW,CAAC,CAACjB,KAAKS,MAAM;QACxBS,aAAa,CAAC,CAAClB,KAAKQ,QAAQ;IAC9B;IAEA,IAAIN,aAAaiB,OAAO,EAAE;QACxBlB,kBAAkB;IACpB;IACA,OAAOA;AACT;AAEO,eAAeJ,qBACpBG,IAAe,EACfoB,KAA8B,EAC9BC,IAAS;QAEHrB,6BAAAA;IAAN,QAAMA,qBAAAA,KAAKsB,YAAY,sBAAjBtB,8BAAAA,mBAAmBuB,QAAQ,qBAA3BvB,4BAA6BH,oBAAoB,CAACG,KAAKI,GAAG,EAAEgB,OAAOC;AAC3E;AAEA,eAAeG,aACbxB,IAEC;IAED,MAAM,EAAEM,UAAU,EAAEG,MAAM,EAAED,QAAQ,EAAEJ,GAAG,EAAEqB,UAAU,EAAE,GAAGzB;IAC1D,MAAM,EAAE0B,yBAAyB,EAAE,GAAGpB;IAEtC,MAAMD,UAAUsB,aAAI,CAACC,IAAI,CAAC5B,KAAKI,GAAG,EAAEJ,KAAKM,UAAU,CAACD,OAAO;IAE3DwB,IAAAA,iBAAS,EAAC,WAAWxB;IACrBwB,IAAAA,iBAAS,EAAC,SAASC,mCAAwB;IAE3C,MAAMC,mBAAmBC,IAAAA,oCAAsB,EAC7C1B,WAAW2B,cAAc,EACzBxB;IAGF,MAAMyB,eAA6B,CAAC;IAEpC,sEAAsE;IACtEC,YAAY,CAACC,QAAQ,CAAC;QACpBC,SAAS/B,WAAW+B,OAAO,KAAK;IAClC;IAEA,MAAMC,cAA0CtC,KAAKuC,KAAK,GACtD,MAAMC,IAAAA,gDAA0B,EAACxC,MAAMkC,cAAc7B,SAASoB,cAC9D,IAAIgB,2BAAkB,CAACzC,KAAKI,GAAG,EAAE;QAC/BsC,UAAU1C,KAAK0C,QAAQ;QACvBjC;QACAD;QACAH;QACAsC,QAAQ3C,KAAKM,UAAU;QACvBsC,SAAS;QACTC,eAAe,MAAMC,IAAAA,kDAA2B,EAAC;YAC/CC,SAAS;YACT1C;QACF;QACA2C,WAAWhD,KAAKgD,SAAS;QACzBC,UAAUjD,KAAKkD,SAAS,CAACD,QAAQ;QACjCE,cAAcnD,KAAKkD,SAAS,CAACE,iBAAiB,CAACC,OAAO;QACtD5B;IACF;IAEJ,MAAMa,YAAYgB,KAAK;IAEvB,4DAA4D;IAC5D,sBAAsB;IACtB,MAAMC,eAAe5B,aAAI,CAACC,IAAI,CAACvB,SAAS;IACxC,MAAMmD,IAAAA,wCAAuB,EAC3B;QACEC,WAAW,CAAC;QACZC,YAAY,CAAC;QACbC,cAAc,CAAC;QACfC,uBAAuB,CAAC;QACxBC,gBAAgB,CAAC;QACjBC,eAAe,CAAC;QAChBC,cAAc,IAAIC;QAClBC,sBAAsB,IAAID;QAC1BE,aAAa,IAAIF;QACjBG,kBAAkB,IAAIH;QACtBI,eAAe,IAAIJ;QACnBK,iBAAiB,IAAIC;IACvB,GACA3C,aAAI,CAACC,IAAI,CAAC2B,cAAc,gBACxBvD,KAAKM,UAAU;IAGjB,MAAML,kBAAkB,MAAMF,iBAAiBC;IAE/C,MAAMuE,qBAAqB5C,aAAI,CAACC,IAAI,CAACvB,SAASmE,0BAAe;IAC7D,MAAMC,iBAAoC;QACxCtD,SAAS;QACTuD,eAAe,CAAC,CAACpE,WAAWqE,YAAY,CAACC,mBAAmB;QAC5DC,UAAUvE,WAAWuE,QAAQ;QAC7B5B,UAAUjD,KAAKkD,SAAS,CAACD,QAAQ;QACjC6B,WAAW9E,KAAKkD,SAAS,CAAC4B,SAAS;QACnCC,SAAS/E,KAAKkD,SAAS,CAAC6B,OAAO;QAC/BC,MAAM1E,WAAW0E,IAAI,IAAIC;QACzBC,4BAA4B5E,WAAW4E,0BAA0B;IACnE;IACA,MAAMC,WAAE,CAACC,QAAQ,CAACC,SAAS,CACzBd,oBACAe,KAAKC,SAAS,CAACd;IAGjB,MAAMe,wBAAwB7D,aAAI,CAACC,IAAI,CAACvB,SAASoF,6BAAkB;IACnE,MAAMN,WAAE,CAACC,QAAQ,CAACC,SAAS,CACzBG,uBACAF,KAAKC,SAAS,CAACvF,KAAKkD,SAAS,CAACE,iBAAiB,EAAE,MAAM;IAGzD,IAAIpD,KAAKM,UAAU,CAACqE,YAAY,CAACe,iBAAiB,EAAE;QAClD,MAAMC,IAAAA,0CAAoB,EACxB3F,KAAKI,GAAG,EACRuB,aAAI,CAACC,IAAI,CAACvB,SAASuF,mCAAwB;IAE/C;IAEA5F,KAAKkD,SAAS,CAAC2C,cAAc,CAAC,eAAeC,OAAOC,IAAI;QACtD,IAAIA,KAAKC,IAAI,KAAK,aAAaD,KAAKC,IAAI,KAAK,YAAY;YACvD,MAAM1D,YAAY2D,UAAU,CAAC;gBAC3BC,YAAY;gBACZC,MAAMJ,KAAKK,QAAQ;gBACnBC,OAAON,KAAKC,IAAI,KAAK;gBACrBM,YAAYrB;YACd;QACF;IACF;IAEA,IAAIsB,WAAW;IACf,IAAIC,mBAA6B,EAAE;IAEnC,MAAM,IAAIC,QAAc,OAAOC,SAASC;QACtC,IAAInG,UAAU;YACZ,yDAAyD;YACzD2E,WAAE,CAACyB,OAAO,CAACpG,UAAU,CAACqG,GAAGC;gBACvB,IAAIA,yBAAAA,MAAOC,MAAM,EAAE;oBACjB;gBACF;gBAEA,IAAI,CAACR,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF;QACF;QAEA,MAAMS,QAAQxG,WAAW;YAACA;SAAS,GAAG,EAAE;QACxC,MAAMyG,MAAMxG,SAAS;YAACA;SAAO,GAAG,EAAE;QAClC,MAAMyG,cAAc;eAAIF;eAAUC;SAAI;QAEtC,MAAME,UAAU3G,YAAYC;QAC5B,MAAMqG,QAAQ;eACTM,IAAAA,sCAA8B,EAC/BzF,aAAI,CAACC,IAAI,CAACuF,SAAU,OACpB7G,WAAW2B,cAAc;eAExBoF,IAAAA,+CAAuC,EACxC1F,aAAI,CAACC,IAAI,CAACuF,SAAU,OACpB7G,WAAW2B,cAAc;SAE5B;QACD,IAAIqF,mBAA6B,EAAE;QAEnC,MAAMC,WAAW;YACf;YACA;YACA;YACA;SACD,CAACC,GAAG,CAAC,CAACC,OAAS9F,aAAI,CAACC,IAAI,CAACxB,KAAKqH;QAE/BX,MAAMY,IAAI,IAAIH;QAEd,wCAAwC;QACxC,MAAMI,gBAAgB;YACpBhG,aAAI,CAACC,IAAI,CAACxB,KAAK;YACfuB,aAAI,CAACC,IAAI,CAACxB,KAAK;SAChB;QACD0G,MAAMY,IAAI,IAAIC;QAEd,MAAMC,KAAK,IAAIC,kBAAS,CAAC;YACvBC,SAAS,CAACC;gBACR,OACE,CAACjB,MAAMkB,IAAI,CAAC,CAACP,OAASA,KAAKQ,UAAU,CAACF,cACtC,CAACb,YAAYc,IAAI,CACf,CAACE,IAAMH,SAASE,UAAU,CAACC,MAAMA,EAAED,UAAU,CAACF;YAGpD;QACF;QACA,MAAMI,iBAAiB,IAAI7D;QAC3B,IAAI8D,oBAAoBnI;QACxB,IAAIoI;QACJ,IAAIC,+BAA4C,IAAItE;QAEpD,MAAMuE,qBAAqB5G,aAAI,CAACC,IAAI,CAACvB,SAAS,SAAS;QACvD,MAAMmI,oBAAoB7G,aAAI,CAACC,IAAI,CAACvB,SAAS,SAAS;QAEtDuH,GAAGa,EAAE,CAAC,cAAc;gBAsjBiBvG,0BACLA;YAtjB9B,IAAIwG;YACJ,MAAMC,cAAwB,EAAE;YAChC,MAAMC,aAAahB,GAAGiB,kBAAkB;YACxC,MAAMC,WAAqC,CAAC;YAC5C,MAAMC,cAAc,IAAI/E;YACxB,MAAMgF,0BAA0B,IAAIhF;YACpC,MAAMiF,mBAAmB,IAAI3E;YAC7B,MAAM4E,qBAAqB,IAAI5E;YAC/B,MAAMH,mBAA+D,EAAE;YACvE,MAAMC,gBAA4D,EAAE;YAEpE,MAAMV,aAAyD,EAAE;YACjE,MAAMD,YAAwD,EAAE;YAChE,MAAME,eAA2D,EAAE;YACnE,MAAMwF,QAAiD,EAAE;YAEzD,IAAIC,YAAY;YAChB,IAAIC,iBAAiB;YACrB,IAAIC,wBAAwB;YAC5B,IAAIC,qBAAqB;YAEzB,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGzJ,KAAKkD,SAAS;YAE9CsG,SAASE,KAAK;YACdD,UAAUC,KAAK;YACfC,qBAAY,CAACD,KAAK;YAElB,MAAME,mBAA6B;mBAAIhB,WAAWiB,IAAI;aAAG,CAACC,IAAI,CAC5DC,IAAAA,uBAAc,EAACzJ,WAAW2B,cAAc;YAG1C,KAAK,MAAM+H,YAAYJ,iBAAkB;gBACvC,IACE,CAAC9C,MAAMmD,QAAQ,CAACD,aAChB,CAAC9C,YAAYc,IAAI,CAAC,CAACE,IAAM8B,SAAS/B,UAAU,CAACC,KAC7C;oBACA;gBACF;gBACA,MAAMgC,OAAOtB,WAAWuB,GAAG,CAACH;gBAE5B,MAAMI,YAAYjC,eAAegC,GAAG,CAACH;gBACrC,gGAAgG;gBAChG,MAAMK,kBACJD,cAAcnF,aACbmF,aAAaA,eAAcF,wBAAAA,KAAMI,SAAS;gBAC7CnC,eAAeoC,GAAG,CAACP,UAAUE,wBAAAA,KAAMI,SAAS;gBAE5C,IAAI/C,SAAS0C,QAAQ,CAACD,WAAW;oBAC/B,IAAIK,iBAAiB;wBACnBjB,YAAY;oBACd;oBACA;gBACF;gBAEA,IAAIzB,cAAcsC,QAAQ,CAACD,WAAW;oBACpC,IAAIA,SAASQ,QAAQ,CAAC,kBAAkB;wBACtCpC,oBAAoB;oBACtB;oBACA,IAAIiC,iBAAiB;wBACnBhB,iBAAiB;oBACnB;oBACA;gBACF;gBAEA,IACEa,CAAAA,wBAAAA,KAAMO,QAAQ,MAAKxF,aACnB,CAAClD,iBAAiB2I,UAAU,CAACV,WAC7B;oBACA;gBACF;gBAEA,MAAMW,YAAYhK,QAChBF,UACEmK,IAAAA,kCAAgB,EAACZ,UAAU/B,UAAU,CACnC2C,IAAAA,kCAAgB,EAACnK,UAAU;gBAGjC,MAAMoK,aAAalK,QACjBH,YACEoK,IAAAA,kCAAgB,EAACZ,UAAU/B,UAAU,CACnC2C,IAAAA,kCAAgB,EAACpK,YAAY;gBAInC,MAAMsK,WAAWC,IAAAA,sCAAkB,EAACf,UAAU;oBAC5C5J,KAAKA;oBACL4K,YAAY1K,WAAW2B,cAAc;oBACrCgJ,WAAW;oBACXC,WAAWC,qBAAU,CAACC,IAAI;gBAC5B;gBAEA,IAAIC,IAAAA,wBAAgB,EAACP,WAAW;wBAsBTQ;oBArBrB,MAAMA,aAAa,MAAMC,IAAAA,sCAA6B,EAAC;wBACrDC,cAAcxB;wBACdrH,QAAQrC;wBACRG,QAAQA;wBACR0F,MAAM2E;wBACNW,OAAO;wBACPC,gBAAgBf;wBAChB1I,gBAAgB3B,WAAW2B,cAAc;oBAC3C;oBACA,IAAI3B,WAAWqL,MAAM,KAAK,UAAU;wBAClCC,KAAIC,KAAK,CACP;wBAEF;oBACF;oBACA3J,aAAa4J,oBAAoB,GAAGhB;oBACpC,MAAMjL,qBACJG,MACA,wBACAkC,aAAa4J,oBAAoB;oBAEnCpD,qBAAqB4C,EAAAA,yBAAAA,WAAWS,UAAU,qBAArBT,uBAAuBU,QAAQ,KAAI;wBACtD;4BAAEC,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBACD;gBACF;gBACA,IAAIC,IAAAA,iCAAyB,EAACrB,WAAW;oBACvC5I,aAAakK,6BAA6B,GAAGtB;oBAC7C,MAAMjL,qBACJG,MACA,iCACAkC,aAAakK,6BAA6B;oBAE5C;gBACF;gBAEA,IAAIpC,SAASQ,QAAQ,CAAC,UAAUR,SAASQ,QAAQ,CAAC,SAAS;oBACzDpC,oBAAoB;gBACtB;gBAEA,IAAI,CAAEuC,CAAAA,aAAaE,UAAS,GAAI;oBAC9B;gBACF;gBAEA,yDAAyD;gBACzDlB,qBAAY,CAAC0C,GAAG,CAACrC;gBAEjB,IAAIsC,WAAWvB,IAAAA,sCAAkB,EAACf,UAAU;oBAC1C5J,KAAKuK,YAAYlK,SAAUD;oBAC3BwK,YAAY1K,WAAW2B,cAAc;oBACrCgJ,WAAWN;oBACXO,WAAWP,YAAYQ,qBAAU,CAACoB,GAAG,GAAGpB,qBAAU,CAACqB,KAAK;gBAC1D;gBAEA,IACE7B,aACAlK,UACAgM,IAAAA,oCAAmB,EACjBzC,SAAS0C,OAAO,CAACjM,QAAQ,KACzBH,WAAW2B,cAAc,EACzB,OAEF;oBACA,MAAMqJ,aAAa,MAAMqB,IAAAA,oCAAiB,EAAC;wBACzCnB,cAAcxB;wBACd1J,YAAY,CAAC;wBACb6F,MAAMmG;wBACNb,OAAO;wBACPmB,UAAUzB,qBAAU,CAACoB,GAAG;oBAC1B;oBAEAD,WAAWO,IAAAA,8CAA4B,EACrCP,UACA,CAAC,CAAEhB,CAAAA,WAAWwB,gBAAgB,IAAIxB,WAAWyB,qBAAqB,AAAD;gBAErE;gBAEA,IACE,CAACpC,aACD2B,SAASrE,UAAU,CAAC,YACpB3H,WAAWqL,MAAM,KAAK,UACtB;oBACAC,KAAIC,KAAK,CACP;oBAEF;gBACF;gBAEA,IAAIlB,WAAW;oBACb,MAAMqC,iBAAiBjL,iBAAiBiL,cAAc,CAAChD;oBACvDT,qBAAqB;oBAErB,IAAIyD,gBAAgB;wBAClB;oBACF;oBAEA,kEAAkE;oBAClE,IAAIpC,IAAAA,kCAAgB,EAAC0B,UAAUrC,QAAQ,CAAC,OAAO;wBAC7C;oBACF;oBAEA,gDAAgD;oBAChD,mDAAmD;oBACnD,8DAA8D;oBAC9D,MAAMgD,WAAWrC,IAAAA,kCAAgB,EAAC0B,UAAUY,KAAK,CAAC;oBAClD,IAAK,IAAIC,IAAIF,SAASlG,MAAM,GAAG,GAAGoG,KAAK,GAAGA,IAAK;wBAC7C,MAAMC,UAAUH,QAAQ,CAACE,EAAE;wBAC3B,IAAIE,IAAAA,+BAAsB,EAACD,UAAU;4BACnC,MAAME,aAAaC,IAAAA,0BAAgB,EACjCN,SAASO,KAAK,CAAC,GAAGL,GAAGvL,IAAI,CAAC;4BAG5B,MAAM6L,WAAWL,QAAQI,KAAK,CAAC;4BAC/B,mCAAmC;4BACnC,IACErE,MAAMnB,IAAI,CACR,CAAC0F,IAAMA,EAAEC,IAAI,KAAKF,YAAYC,EAAEE,MAAM,KAAKN,aAG7C;4BAEFnE,MAAMzB,IAAI,CAAC;gCACTiG,MAAMF;gCACNG,QAAQN;4BACV;4BACA;wBACF;oBACF;oBAEA,iBAAiB;oBACjB,IAAIvL,iBAAiB8L,eAAe,CAAC7D,WAAW;wBAC9CrG,aAAa+D,IAAI,CAAC;4BAChBoG,OAAOC,IAAAA,sCAAkB,EACvBR,IAAAA,0BAAgB,EAAC3C,IAAAA,kCAAgB,EAAC0B,WAAWI,OAAO,CAClD,aACA;4BAGJsB,UAAUhE;wBACZ;oBACF;oBAEA,IAAI,CAACjI,iBAAiBkM,eAAe,CAACjE,WAAW;wBAC/C;oBACF;oBAEA,MAAMkE,mBAAmB5B;oBACzBA,WAAWiB,IAAAA,0BAAgB,EAACjB,UAAUI,OAAO,CAAC,QAAQ;oBACtD,IAAI,CAAC5D,QAAQ,CAACwD,SAAS,EAAE;wBACvBxD,QAAQ,CAACwD,SAAS,GAAG,EAAE;oBACzB;oBACAxD,QAAQ,CAACwD,SAAS,CAAC5E,IAAI,CACrB1H,KAAKuC,KAAK,GAEN2L,iBAAiBxB,OAAO,CAAC,QAAQ,OACjCwB;oBAGN,IAAIxM,2BAA2B;wBAC7B8H,SAAS6C,GAAG,CAACC;oBACf;oBAEA,IAAIvK,iBAAiBoM,gBAAgB,CAACnE,WAAW;wBAC/C7F,iBAAiBuD,IAAI,CAAC;4BACpBoG,OAAOlD,IAAAA,kCAAgB,EAAC0B;4BACxB0B,UAAUhE;wBACZ;oBACF,OAAO;wBACLvG,UAAUiE,IAAI,CAAC;4BACboG,OAAOlD,IAAAA,kCAAgB,EAAC0B;4BACxB0B,UAAUhE;wBACZ;oBACF;oBAEA,IAAIrB,YAAYsB,QAAQ,CAACqC,WAAW;wBAClC;oBACF;gBACF,OAAO;oBACL,IAAI5K,2BAA2B;wBAC7B+H,UAAU4C,GAAG,CAACC;wBACd,8DAA8D;wBAC9D,8DAA8D;wBAC9DtM,KAAKkD,SAAS,CAACkL,cAAc,CAAC/B,GAAG,CAACC;oBACpC;oBAEA,IAAIA,SAASrE,UAAU,CAAC,UAAU;wBAChC7D,cAAcsD,IAAI,CAAC;4BACjBoG,OAAOlD,IAAAA,kCAAgB,EAAC0B;4BACxB0B,UAAUhE;wBACZ;oBACF,OAAO;wBACLtG,WAAWgE,IAAI,CAAC;4BACdoG,OAAOlD,IAAAA,kCAAgB,EAAC0B;4BACxB0B,UAAUhE;wBACZ;oBACF;gBACF;gBAEA,eAAe;gBACf,IAAIW,WAAW;oBACb1B,iBAAiBsB,GAAG,CAAC+B,UAAUtC;gBACjC,OAAO;oBACLd,mBAAmBqB,GAAG,CAAC+B,UAAUtC;gBACnC;gBAEA,IAAIvJ,UAAUsI,YAAYsF,GAAG,CAAC/B,WAAW;oBACvCtD,wBAAwBqD,GAAG,CAACC;gBAC9B,OAAO;oBACLvD,YAAYsD,GAAG,CAACC;gBAClB;gBAEA;;;SAGC,GACD,IAAI,sBAAsBgC,IAAI,CAAChC,WAAW;oBACxChF,iBAAiBI,IAAI,CAAC4E;oBACtB;gBACF;gBAEA3D,YAAYjB,IAAI,CAAC4E;YACnB;YAEA,MAAMiC,iBAAiBvF,wBAAwBwF,IAAI;YACnDlF,wBAAwBiF,iBAAiBjG,6BAA6BkG,IAAI;YAE1E,IAAIlF,0BAA0B,GAAG;gBAC/B,IAAIiF,iBAAiB,GAAG;oBACtB,IAAIE,eAAe,CAAC,6BAA6B,EAC/CF,mBAAmB,IAAI,SAAS,SACjC,0DAA0D,CAAC;oBAE5D,KAAK,MAAMG,KAAK1F,wBAAyB;wBACvC,MAAM2F,UAAUhN,aAAI,CAACiN,QAAQ,CAACxO,KAAK6I,iBAAiBkB,GAAG,CAACuE;wBACxD,MAAMG,YAAYlN,aAAI,CAACiN,QAAQ,CAACxO,KAAK8I,mBAAmBiB,GAAG,CAACuE;wBAC5DD,gBAAgB,CAAC,GAAG,EAAEI,UAAU,KAAK,EAAEF,QAAQ,GAAG,CAAC;oBACrD;oBACArM,YAAYwM,iBAAiB,CAAC,qBAAuB,CAAvB,IAAIC,MAAMN,eAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsB;gBACtD,OAAO,IAAIF,mBAAmB,GAAG;oBAC/BjM,YAAY0M,mBAAmB;oBAC/B,MAAMnP,qBAAqBG,MAAM,kBAAkBiF;gBACrD;YACF;YAEAqD,+BAA+BU;YAE/B,IAAIiG;YACJ,IAAI3O,WAAWqE,YAAY,CAACuK,kBAAkB,EAAE;gBAC9CD,sBAAsBE,IAAAA,kDAAwB,EAC5CC,OAAOvF,IAAI,CAACf,WACZxI,WAAWqE,YAAY,CAAC0K,2BAA2B,GAC/C,AAAC,CAAA,AAAC/O,WAAmBgP,kBAAkB,IAAI,EAAE,AAAD,EAAG5O,MAAM,CACnD,CAAC6O,IAAW,CAACA,EAAEC,QAAQ,IAEzB,EAAE,EACNlP,WAAWqE,YAAY,CAAC8K,6BAA6B;gBAGvD,IACE,CAACpH,+BACD/C,KAAKC,SAAS,CAAC8C,iCACb/C,KAAKC,SAAS,CAAC0J,sBACjB;oBACA7F,YAAY;oBACZf,8BAA8B4G;gBAChC;YACF;YAEA,IAAI,CAAChP,mBAAmBmI,mBAAmB;gBACzC,oDAAoD;gBACpD,+CAA+C;gBAC/C,MAAMrI,iBAAiBC,MACpB0P,IAAI,CAAC;oBACJrG,iBAAiB;gBACnB,GACCsG,KAAK,CAAC,KAAO;YAClB;YAEA,IAAIvG,aAAaC,gBAAgB;oBAyE/B/G;gBAxEA,IAAI8G,WAAW;wBAWU9I;oBAVvB,MAAM,EAAEsP,cAAc,EAAE,GAAGC,IAAAA,kBAAa,EACtCzP,KACA0P,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBpE,MACA,MACA,CAACqE;wBACCrE,KAAIsE,IAAI,CAAC,CAAC,YAAY,EAAED,aAAa;oBACvC;oBAGF,IAAIhQ,qBAAmBK,2BAAAA,WAAWqE,YAAY,qBAAvBrE,yBAAyB6P,QAAQ,GAAE;wBACxD,0DAA0D;wBAC1DC,IAAAA,0CAAoB,EAAC;4BACnB/P;4BACAuP,gBAAgB;mCACXA;gCACH;oCACEjO,MAAMrB,WAAW+P,cAAc;oCAC/BN,KAAKzP,WAAWyP,GAAG;oCACnBO,UAAU;gCACZ;6BACD;wBACH;oBACF;oBAEA,MAAMzQ,qBAAqBG,MAAM,iBAAiB;wBAChD;4BAAEuQ,KAAK;4BAAMC,aAAa;4BAAMC,QAAQ;wBAAK;qBAC9C;gBACH;gBACA,IAAIC;gBAIJ,IAAIrH,gBAAgB;oBAClB,IAAI;wBACFqH,iBAAiB,MAAMC,IAAAA,qBAAY,EAACvQ,KAAKE;oBAC3C,EAAE,OAAOuG,GAAG;oBACV,2EAA2E,GAC7E;gBACF;gBAEA,IAAIvE,YAAYsO,gBAAgB,EAAE;wBAO9B5Q;oBANF,MAAM6Q,cACJ7Q,KAAKkD,SAAS,CAACD,QAAQ,CAAC6N,UAAU,CAAC/J,MAAM,GAAG,KAC5C/G,KAAKkD,SAAS,CAACD,QAAQ,CAAC8N,WAAW,CAAChK,MAAM,GAAG,KAC7C/G,KAAKkD,SAAS,CAACD,QAAQ,CAAC+N,QAAQ,CAACjK,MAAM,GAAG;oBAE5C,MAAMkK,WACJjR,EAAAA,6BAAAA,KAAKM,UAAU,CAAC4Q,SAAS,qBAAzBlR,2BAA2BmR,IAAI,KAC/BnR,KAAKM,UAAU,CAAC8Q,qBAAqB,IACrCpR,KAAKI,GAAG;oBACV,MAAMkC,YAAYsO,gBAAgB,CAACS,MAAM,CAAC;wBACxCC,WAAWC,IAAAA,oBAAe,EAAC;4BACzBC,aAAa;4BACbvC;4BACAtM,QAAQrC;4BACRiQ,KAAK;4BACLlQ;4BACAoR,qBACEzR,KAAKM,UAAU,CAACqE,YAAY,CAAC8M,mBAAmB;4BAClDZ;4BACA,kBAAkB;4BAClBnI,oBAAoBzD;4BACpByM,aAAa1R,KAAKI,GAAG;4BACrB6C,UAAUjD,KAAKkD,SAAS,CAACD,QAAQ;wBACnC;wBACAgO;wBACAS,aAAaC,IAAAA,4BAAa,EAAChQ,aAAI,CAACiN,QAAQ,CAACqC,UAAU7Q;oBACrD;gBACF;iBAEAkC,oCAAAA,YAAYsP,oBAAoB,qBAAhCtP,kCAAkCuP,OAAO,CAAC,CAAClP,QAAQmP;oBACjD,MAAMC,WAAWD,QAAQ;oBACzB,MAAME,eAAeF,QAAQ;oBAC7B,MAAMG,eAAeH,QAAQ;oBAC7B,MAAMjB,cACJ7Q,KAAKkD,SAAS,CAACD,QAAQ,CAAC6N,UAAU,CAAC/J,MAAM,GAAG,KAC5C/G,KAAKkD,SAAS,CAACD,QAAQ,CAAC8N,WAAW,CAAChK,MAAM,GAAG,KAC7C/G,KAAKkD,SAAS,CAACD,QAAQ,CAAC+N,QAAQ,CAACjK,MAAM,GAAG;oBAE5C,IAAIsC,gBAAgB;4BAClB1G,yBAAAA;yBAAAA,kBAAAA,OAAO+D,OAAO,sBAAd/D,0BAAAA,gBAAgBuP,OAAO,qBAAvBvP,wBAAyBkP,OAAO,CAAC,CAACM;4BAChC,mDAAmD;4BACnD,kCAAkC;4BAClC,IAAIA,kBAAkBC,wCAAmB,IAAI1B,gBAAgB;oCAGlC/N,yBAAAA,iBAqBrB0P;gCAvBJ,MAAM,EAAEC,eAAe,EAAED,QAAQ,EAAE,GAAG3B;gCACtC,MAAM6B,yBAAyBJ,OAAOG,eAAe;gCACrD,MAAME,oBAAmB7P,kBAAAA,OAAO+D,OAAO,sBAAd/D,0BAAAA,gBAAgB8P,OAAO,qBAAvB9P,wBAAyB+P,SAAS,CACzD,CAAC3M,OAASA,UAASwM,0CAAAA,uBAAwBI,OAAO;gCAGpD,IAAIL,iBAAiB;oCACnB,IACEA,gBAAgBK,OAAO,MAAKJ,0CAAAA,uBAAwBI,OAAO,GAC3D;wCACA,qCAAqC;wCACrC,IAAIH,oBAAoBA,mBAAmB,CAAC,GAAG;gDAC7C7P,0BAAAA;6CAAAA,mBAAAA,OAAO+D,OAAO,sBAAd/D,2BAAAA,iBAAgB8P,OAAO,qBAAvB9P,yBAAyBiQ,MAAM,CAACJ,kBAAkB;wCACpD;wCAEA,wEAAwE;wCACxE,mEAAmE;wCACnE,IAAI,CAACF,gBAAgBO,UAAU,EAAE;gDAC/BlQ,0BAAAA;6CAAAA,mBAAAA,OAAO+D,OAAO,sBAAd/D,2BAAAA,iBAAgB8P,OAAO,qBAAvB9P,yBAAyB+E,IAAI,CAAC4K,gBAAgBK,OAAO;wCACvD;oCACF;gCACF;gCAEA,IAAIN,CAAAA,6BAAAA,4BAAAA,SAAUS,eAAe,qBAAzBT,0BAA2BU,KAAK,KAAIT,iBAAiB;oCACvDlD,OAAOvF,IAAI,CAACsI,OAAOY,KAAK,EAAElB,OAAO,CAAC,CAACmB;wCACjC,OAAOb,OAAOY,KAAK,CAACC,IAAI;oCAC1B;oCACA5D,OAAO6D,MAAM,CAACd,OAAOY,KAAK,EAAEV,SAASS,eAAe,CAACC,KAAK;oCAC1DZ,OAAOG,eAAe,GAAGA;gCAC3B;4BACF;wBACF;oBACF;oBAEA,IAAIlJ,WAAW;4BACbzG;yBAAAA,kBAAAA,OAAOuP,OAAO,qBAAdvP,gBAAgBkP,OAAO,CAAC,CAACM;4BACvB,qDAAqD;4BACrD,sCAAsC;4BACtC,IACEA,UACA,OAAOA,OAAOe,WAAW,KAAK,YAC9Bf,OAAOe,WAAW,CAACC,iBAAiB,EACpC;gCACA,MAAMC,YAAYC,IAAAA,uBAAY,EAAC;oCAC7B7B,aAAa;oCACbvC;oCACAtM,QAAQrC;oCACRiQ,KAAK;oCACLlQ;oCACAoR,qBACEzR,KAAKM,UAAU,CAACqE,YAAY,CAAC8M,mBAAmB;oCAClDZ;oCACAkB;oCACAE;oCACAD;oCACAtJ,oBAAoBzD;oCACpByM,aAAa1R,KAAKI,GAAG;oCACrB6C,UAAUjD,KAAKkD,SAAS,CAACD,QAAQ;gCACnC;gCAEAmM,OAAOvF,IAAI,CAACsI,OAAOe,WAAW,EAAErB,OAAO,CAAC,CAACmB;oCACvC,IAAI,CAAEA,CAAAA,OAAOI,SAAQ,GAAI;wCACvB,OAAOjB,OAAOe,WAAW,CAACF,IAAI;oCAChC;gCACF;gCACA5D,OAAO6D,MAAM,CAACd,OAAOe,WAAW,EAAEE;4BACpC;wBACF;oBACF;gBACF;gBACA,MAAM9Q,YAAYgR,UAAU,CAAC;oBAC3BC,yBAAyBnK;gBAC3B;YACF;YAEA,IAAI9B,iBAAiBP,MAAM,GAAG,GAAG;gBAC/B6E,KAAIC,KAAK,CACP,qBAIC,CAJD,IAAI2H,6BAAqB,CACvBlM,kBACAlH,KACCI,YAAYC,SAHf,qBAAA;2BAAA;gCAAA;kCAAA;gBAIA,GAAEgT,OAAO;gBAEXnM,mBAAmB,EAAE;YACvB;YAEA,sEAAsE;YACtEpF,aAAawR,aAAa,GAAGtE,OAAOuE,WAAW,CAC7CvE,OAAOwE,OAAO,CAAC9K,UAAUtB,GAAG,CAAC,CAAC,CAACqM,GAAGC,EAAE,GAAK;oBAACD;oBAAGC,EAAEhK,IAAI;iBAAG;YAExD,MAAMjK,qBACJG,MACA,iBACAkC,aAAawR,aAAa;YAG5B,gDAAgD;YAChDxR,aAAa6J,UAAU,GAAGrD,qBACtB;gBACEqL,OAAO;gBACP5N,MAAM;gBACN6F,UAAUtD;YACZ,IACAzD;YAEJ,MAAMpF,qBAAqBG,MAAM,cAAckC,aAAa6J,UAAU;YACtE7J,aAAa8R,cAAc,GAAGzK;YAE9BvJ,KAAKkD,SAAS,CAAC+Q,iBAAiB,GAAG/R,EAAAA,2BAAAA,aAAa6J,UAAU,qBAAvB7J,yBAAyB8J,QAAQ,IAChEkI,IAAAA,iDAAyB,GAAChS,4BAAAA,aAAa6J,UAAU,qBAAvB7J,0BAAyB8J,QAAQ,IAC3D/G;YAEJ,MAAMkP,qBAAqBC,IAAAA,sEAAkC,EAC3DhF,OAAOvF,IAAI,CAACf,WACZ9I,KAAKM,UAAU,CAACuE,QAAQ,EACxB2C,GAAG,CAAC,CAACzB,OACLsO,IAAAA,4BAAgB,EACd,wBACAtO,MACA/F,KAAKM,UAAU,CAACuE,QAAQ,EACxB7E,KAAKM,UAAU,CAACqE,YAAY,CAACC,mBAAmB;YAIpD5E,KAAKkD,SAAS,CAACD,QAAQ,CAAC8N,WAAW,CAACrJ,IAAI,IAAIyM;YAE5C,MAAMG,gBACJ,AAAC,OAAOhU,WAAWgU,aAAa,KAAK,cAClC,OAAMhU,WAAWgU,aAAa,oBAAxBhU,WAAWgU,aAAa,MAAxBhU,YACL,CAAC,GACD;gBACEiQ,KAAK;gBACLnQ,KAAKJ,KAAKI,GAAG;gBACbmU,QAAQ;gBACRlU,SAASA;gBACTuC,SAAS;YACX,OAEJ,CAAC;YAEH,MAAM4R,uBAAuBpF,OAAOwE,OAAO,CAACU,iBAAiB,CAAC;YAE9D,IAAIE,qBAAqBzN,MAAM,GAAG,GAAG;gBACnC/G,KAAKkD,SAAS,CAACuR,mBAAmB,GAAGD,qBAAqBhN,GAAG,CAC3D,CAAC,CAACwL,KAAK0B,MAAM,GACXL,IAAAA,4BAAgB,EACd,wBACA;wBACEM,QAAQ3B;wBACR4B,aAAa,GAAGF,MAAMvO,IAAI,GACxBuO,MAAMG,KAAK,GAAG,MAAM,KACnBC,oBAAE,CAACvP,SAAS,CAACmP,MAAMG,KAAK,GAAG;oBAChC,GACA7U,KAAKM,UAAU,CAACuE,QAAQ,EACxB7E,KAAKM,UAAU,CAACqE,YAAY,CAACC,mBAAmB;YAGxD;YAEA,IAAI;gBACF,gEAAgE;gBAChE,qEAAqE;gBACrE,kEAAkE;gBAClE,MAAMmQ,eAAeC,IAAAA,sBAAe,EAACrM;gBAErC3I,KAAKkD,SAAS,CAAC+R,aAAa,GAAGF,aAAavN,GAAG,CAC7C,CAACrB;oBACC,MAAM+O,QAAQC,IAAAA,yBAAa,EAAChP;oBAC5B,OAAO;wBACL+O,OAAOA,MAAME,EAAE,CAACC,QAAQ;wBACxBtB,OAAOuB,IAAAA,6BAAe,EAACJ;wBACvB/O;oBACF;gBACF;gBAGF,MAAMoP,aAAkD,EAAE;gBAE1D,KAAK,MAAMpP,QAAQ4O,aAAc;oBAC/B,MAAMjH,QAAQ0H,IAAAA,8BAAc,EAACrP,MAAM;oBACnC,MAAMsP,aAAaN,IAAAA,yBAAa,EAACrH,MAAM3H,IAAI;oBAC3CoP,WAAW7N,IAAI,CAAC;wBACd,GAAGoG,KAAK;wBACRoH,OAAOO,WAAWL,EAAE,CAACC,QAAQ;wBAC7BtB,OAAOuB,IAAAA,6BAAe,EAAC;4BACrB,+DAA+D;4BAC/D,uCAAuC;4BACvCF,IAAIpV,KAAKM,UAAU,CAAC0E,IAAI,GACpB,IAAI0Q,OACF5H,MAAM6H,cAAc,CAACjJ,OAAO,CAC1B,CAAC,aAAa,CAAC,EACf,CAAC,mCAAmC,CAAC,KAGzC,IAAIgJ,OAAO5H,MAAM6H,cAAc;4BACnCC,QAAQH,WAAWG,MAAM;wBAC3B;oBACF;gBACF;gBACA5V,KAAKkD,SAAS,CAAC+R,aAAa,CAACY,OAAO,IAAIN;gBAExC,IAAI,EAAC/O,oCAAAA,iBAAkBsP,KAAK,CAAC,CAACC,KAAKjE,MAAQiE,QAAQhB,YAAY,CAACjD,IAAI,IAAG;oBACrE,MAAMkE,cAAcjB,aAAarU,MAAM,CACrC,CAACoN,QAAU,CAACtH,iBAAiByD,QAAQ,CAAC6D;oBAExC,MAAMmI,gBAAgBzP,iBAAiB9F,MAAM,CAC3C,CAACoN,QAAU,CAACiH,aAAa9K,QAAQ,CAAC6D;oBAGpC,8CAA8C;oBAC9CxL,YAAY4T,IAAI,CAAC;wBACfC,QAAQC,6CAA2B,CAACC,yBAAyB;wBAC7DC,MAAM;4BACJ;gCACEC,kBAAkB;4BACpB;yBACD;oBACH;oBAEAP,YAAYnE,OAAO,CAAC,CAAC/D;wBACnBxL,YAAY4T,IAAI,CAAC;4BACfC,QAAQC,6CAA2B,CAACI,UAAU;4BAC9CF,MAAM;gCAACxI;6BAAM;wBACf;oBACF;oBAEAmI,cAAcpE,OAAO,CAAC,CAAC/D;wBACrBxL,YAAY4T,IAAI,CAAC;4BACfC,QAAQC,6CAA2B,CAACK,YAAY;4BAChDH,MAAM;gCAACxI;6BAAM;wBACf;oBACF;gBACF;gBACAtH,mBAAmBuO;gBAEnB,IAAI9U,iBAAiB;oBACnB,MAAMyW,qBAAqB,MAAMC,IAAAA,yCAAwB,EAAC;wBACxDvW;wBACAsD;wBACAD;wBACAE;wBACAwF;wBACArE,WAAW9E,KAAKM,UAAU,CAACwE,SAAS;wBACpC7B,UAAUjD,KAAKM,UAAU,CAAC2C,QAAQ;wBAClC,6EAA6E;wBAC7E,8CAA8C;wBAC9CuF;wBACArE;wBACAC;oBACF;oBAEA,MAAMZ,IAAAA,wCAAuB,EAC3BkT,oBACAnO,oBACAvI,KAAKM,UAAU;oBAEjB,MAAMsW,IAAAA,mCAAkB,EAACF,oBAAoBlO;gBAC/C;gBAEA,IAAI,CAACjC,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF,EAAE,OAAOsQ,GAAG;gBACV,IAAI,CAACtQ,UAAU;oBACbI,OAAOkQ;oBACPtQ,WAAW;gBACb,OAAO;oBACLqF,KAAIkL,IAAI,CAAC,oCAAoCD;gBAC/C;YACF,SAAU;gBACR,kEAAkE;gBAClE,4DAA4D;gBAC5D,MAAMhX,qBAAqBG,MAAM,kBAAkBiF;YACrD;QACF;QAEA2C,GAAGmP,KAAK,CAAC;YAAE7P,aAAa;gBAAC9G;aAAI;YAAE4W,WAAW;QAAE;IAC9C;IAEA,MAAMC,0BAA0B,CAAC,OAAO,EAAErR,mCAAwB,CAAC,aAAa,EAAEsR,oCAAyB,EAAE;IAC7GlX,KAAKkD,SAAS,CAACiU,iBAAiB,CAAC9K,GAAG,CAAC4K;IAErC,MAAMG,4BAA4B,CAAC,OAAO,EAAExR,mCAAwB,CAAC,aAAa,EAAEyR,yCAA8B,EAAE;IACpHrX,KAAKkD,SAAS,CAACiU,iBAAiB,CAAC9K,GAAG,CAAC+K;IAErC,MAAME,qCAAqC,CAAC,OAAO,EAAE1R,mCAAwB,CAAC,aAAa,EAAE2R,+CAAoC,EAAE;IACnIvX,KAAKkD,SAAS,CAACiU,iBAAiB,CAAC9K,GAAG,CAACiL;IAErC,eAAeE,eAAeC,GAAoB,EAAEC,GAAmB;YAGjEC,qBAcFA,sBACAA;QAjBF,MAAMA,YAAYC,YAAG,CAACC,KAAK,CAACJ,IAAIG,GAAG,IAAI;QAEvC,KAAID,sBAAAA,UAAU5P,QAAQ,qBAAlB4P,oBAAoB1N,QAAQ,CAACgN,0BAA0B;YACzDS,IAAII,UAAU,GAAG;YACjBJ,IAAIK,SAAS,CAAC,gBAAgBC,oCAAwB;YACtDN,IAAIO,GAAG,CACL3S,KAAKC,SAAS,CAAC;gBACbyB,OAAOR,iBAAiB9F,MAAM,CAC5B,CAACoN,QAAU,CAAC9N,KAAKkD,SAAS,CAACsG,QAAQ,CAAC6E,GAAG,CAACP;YAE5C;YAEF,OAAO;gBAAEoK,UAAU;YAAK;QAC1B;QAEA,IACEP,EAAAA,uBAAAA,UAAU5P,QAAQ,qBAAlB4P,qBAAoB1N,QAAQ,CAACmN,iCAC7BO,uBAAAA,UAAU5P,QAAQ,qBAAlB4P,qBAAoB1N,QAAQ,CAACqN,sCAC7B;gBAGuBpV;YAFvBwV,IAAII,UAAU,GAAG;YACjBJ,IAAIK,SAAS,CAAC,gBAAgBC,oCAAwB;YACtDN,IAAIO,GAAG,CAAC3S,KAAKC,SAAS,CAACrD,EAAAA,2BAAAA,aAAa6J,UAAU,qBAAvB7J,yBAAyB8J,QAAQ,KAAI,EAAE;YAC9D,OAAO;gBAAEkM,UAAU;YAAK;QAC1B;QACA,OAAO;YAAEA,UAAU;QAAM;IAC3B;IAEA,SAASC,0BACPC,GAAY,EACZpS,IAAyE;QAEzE,IAAIoS,eAAeC,wBAAgB,EAAE;YACnC,wDAAwD;YACxDzM,KAAIC,KAAK,CAACuM,IAAI3E,OAAO;QACvB,OAAO,IAAI2E,eAAeE,qCAAsB,EAAE;QAChD,yEAAyE;QACzE,mEAAmE;QACrE,OAAO,IAAItS,SAAS,WAAW;YAC7B4F,KAAIkL,IAAI,CAACsB;QACX,OAAO,IAAIpS,SAAS,WAAW;YAC7B4F,KAAIC,KAAK,CAACuM;QACZ,OAAO,IAAIpS,MAAM;YACf4F,KAAIC,KAAK,CAAC,GAAG7F,KAAK,CAAC,CAAC,EAAEoS;QACxB,OAAO;YACLxM,KAAIC,KAAK,CAACuM;QACZ;IACF;IAEA,OAAO;QACLlW;QACAI;QACAkV;QACAW;QAEA,MAAMI,kBAAiBC,UAAmB;YACxC,IAAI,CAACtW,aAAa4J,oBAAoB,EAAE;YACxC,OAAOxJ,YAAY2D,UAAU,CAAC;gBAC5BE,MAAMjE,aAAa4J,oBAAoB;gBACvC5F,YAAY;gBACZI,YAAYrB;gBACZ2S,KAAKY;YACP;QACF;IACF;AACF;AAEO,eAAe1Y,gBAAgBE,IAAe;IACnD,MAAM0C,WAAWf,aAAI,CAClBiN,QAAQ,CAAC5O,KAAKI,GAAG,EAAEJ,KAAKQ,QAAQ,IAAIR,KAAKS,MAAM,IAAI,IACnDwH,UAAU,CAAC;IAEd,MAAMwQ,SAAS,MAAMjX,aAAa;QAChC,GAAGxB,IAAI;QACP0C;IACF;IAEA1C,KAAKgD,SAAS,CAAC0V,MAAM,CACnBC,IAAAA,uBAAe,EACbhX,aAAI,CAACC,IAAI,CAAC5B,KAAKI,GAAG,EAAEJ,KAAKM,UAAU,CAACD,OAAO,GAC3CL,KAAKM,UAAU,EACf;QACEsY,gBAAgB;QAChBlW;QACAmW,WAAW,CAAC,CAAC7Y,KAAKuC,KAAK;QACvBuW,YAAY;QACZrY,QAAQ,CAAC,CAACT,KAAKS,MAAM;QACrBD,UAAU,CAAC,CAACR,KAAKQ,QAAQ;QACzBuY,gBAAgB,CAAC,CAAC/Y,KAAK+Y,cAAc;QACrCC,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;YAAEC,KAAKlZ,KAAKI,GAAG;QAAC;IAC1D;IAIJ,4CAA4C;IAC5CJ,KAAKgD,SAAS,CAAC0V,MAAM,CAAC;QACpBS,WAAWC,iCAAyB;QACpCC,SAAS;YACPC,aAAa;YACbC,iBAAiBC,IAAAA,kCAA0B,EAACxZ,KAAKM,UAAU,IAAI,IAAI;QACrE;IACF;IAEA,OAAOmY;AACT;CAIA,2DAA2D", "ignoreList": [0]}